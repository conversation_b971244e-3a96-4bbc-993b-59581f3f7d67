{% load static %}

<!-- Navbar-->
<header class="app-header">
    <a class="app-header__logo" href="{% url 'school:home' %}">
      {% if user.school.logo %}
      <img src="{{ user.school.logo.url }}" alt="" height="50px" class="rounded">  
      {% else %}
      <img src="{% static 'img/logo.png' %}" alt="" height="50px" class="rounded">  
      {% endif %}
    </a>
    <!-- Sidebar toggle button-->
    <a class="app-sidebar__toggle my-100 ripple" href="#" data-toggle="sidebar" aria-label="Hide Sidebar" id="sidebarToggleBtn">
      <div class="d-flex flex-column mt-3">
        <i data-feather="menu"></i>
      </div>
    </a>
    <!-- Navbar Right Menu-->
    <ul class="app-nav">
      <!-- User Menu-->
      {% if user.is_authenticated %}
        <a class="pull-bs-canvas-right ml-3 order-xl-last my-auto btn btn-outline-info text-white" href="#"><span data-feather="user"></span></a>
      {% else %}
        <div>
          <a href="{% url 'users:login' %}" class="btn btn-warning mt-1"> <span data-feather="key" class="feather-11"></span> Connectez-vous</a>
        </div>
      {% endif %}
    </ul>
  </header>
  <!-- Sidebar menu-->
  <div class="app-sidebar__overlay" data-toggle="sidebar"></div>
  <aside class="app-sidebar">
    <div class="app-sidebar__user">
      {% if user.is_authenticated %}
      <img class="app-sidebar__user-avatar" width="50px" src="{{ user.get_photo }}" alt="User Image">
      {% else %}
      <img class="app-sidebar__user-avatar" width="50px" src="{% static 'img/profile_pic.png' %}" alt="User Image">
      {% endif %}
      <div>
        <p class="app-sidebar__user-name" style="font-size: 9pt;">{% if user.is_authenticated %} {{ user.get_full_name }} {% else %} Inconnu {% endif %}</p>
        <p class="app-sidebar__user-designation">{% if user.is_authenticated %} {{ user.get_role_display }} {% else %} <a href="{% url 'users:login' %}">Connectez-vous {% endif %}</a> </p>
      </div>
    </div>
    
    {% if user.is_authenticated and school_plan != PLAN_LEVEL %}
    <ul class="app-menu">
      <li><a class="app-menu__item close-on-click" href="{% url 'school:home' %}" 
             hx-get="{% url 'school:home' %}" hx-target="#app-content"
             hx-push-url="{% url 'school:home' %}">
        <i data-feather="home" class="feather-16 mr-2"></i><span class="app-menu__label">Accueil</span></a>
      </li>
      {% if perms.school.view_payment %}
      <li class="treeview" data-toggle="tooltip">
        <a class="app-menu__item" href="#" data-toggle="treeview">
          <i data-feather="dollar-sign" class="feather-16 mr-2 feather"></i>
          <span class="app-menu__label">Comptabilité</span>
          <i data-feather="chevron-right" class="toggler-plus-icon" style="width: 18px; height: 18px;"></i>
        </a>
        <ul class="treeview-menu">
          <li class="close-on-click">
            <a class="treeview-item" href="{% url 'school:students' %}?statut=inscrits" hx-get="{% url 'school:students' %}?statut=inscrits" 
               hx-target="#app-content" hx-push-url="{% url 'school:students' %}?statut=inscrits">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Inscription - Paiements
            </a>
          </li>
          <li class="close-on-click"><a class="treeview-item" 
            href="{% url 'school:payments' %}" rel="noopener"
            hx-get="{% url 'school:payments' %}" 
            hx-target="#app-content" hx-push-url="{% url 'school:payments' %}">
            <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
            Historique des paiements</a>
            </li>
          <li>
            {% if user.school.pricing_option == 0 %}
            <a class="treeview-item close-on-click" href="{% url 'school:pricing' %}" 
               hx-get="{% url 'school:pricing' %}?education=ar" 
               hx-target="#app-content"
               hx-push-url="{% url 'school:pricing' %}?education=ar">
            <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
            Frais d'écolage (Tarifs)</a>

            {% else %}
            <a class="treeview-item close-on-click" href="{% url 'school:pricing' %}" 
               hx-get="{% url 'school:pricing' %}?education=fr" 
               hx-target="#app-content"
               hx-push-url="{% url 'school:pricing' %}?education=fr">
            <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
            Frais d'écolage (Tarifs)</a>
            {% endif %}
          </li>
          <div class="dropdown-divider"></div>
          <li>
            <a class="treeview-item close-on-click" href="{% url 'school:reports' %}?periode=aujourdhui" 
               hx-get="{% url 'school:reports' %}?periode=aujourdhui" 
               hx-target="#app-content"
               hx-push-url="{% url 'school:reports' %}?periode=aujourdhui">
            <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
            Rapport des entrées</a>
          </li>
          <li>
            <a class="treeview-item close-on-click" href="{% url 'school:balance' %}?type=solde" 
               hx-get="{% url 'school:balance' %}?type=solde" 
               hx-target="#app-content"
               hx-push-url="{% url 'school:balance' %}?type=solde">
            <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
            Caisse et Dépenses</a>
          </li>
        </ul>
      </li>
      {% endif %}
      {%if not request.user.role == 'TC' %}
      {% if perms.school.view_enrollment %}
      <li class="treeview"  data-toggle="tooltip">
        <a class="app-menu__item" href="#" data-toggle="treeview">
          <i data-feather="folder" class="feather-16 mr-2 feather"></i>
          <span class="app-menu__label">Gestion des inscrits</span>
          <i data-feather="chevron-right" class="toggler-plus-icon" style="width: 18px; height: 18px;"></i>
        </a>
        <ul class="treeview-menu">
          <li class="close-on-click">
            <a class="treeview-item" href="{% url 'school:active_students' %}" hx-get="{% url 'school:active_students' %}" 
               hx-target="#app-content" hx-push-url="{% url 'school:active_students' %}">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Elèves inscrits
            </a>
          </li>
          <li class="close-on-click">
            <a class="treeview-item" href="{% url 'school:student_edit_list' %}" hx-get="{% url 'school:student_edit_list' %}" 
               hx-target="#app-content" hx-push-url="{% url 'school:student_edit_list' %}">
              <i data-feather="edit" class="feather-11 font-weight-bold mr-2"></i> Mode édition 
              <span class="badge badge-info ml-1">New</span>
            </a>
          </li>
          <li class="close-on-click">
            <a class="treeview-item" href="{% url 'school:students_photos' %}?recents" hx-get="{% url 'school:students_photos' %}?recents" 
               hx-target="#app-content" hx-push-url="{% url 'school:students_photos' %}?recents">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Gestion des photos
            </a>
          </li>
          <li>
            <a class="treeview-item close-on-click" href="{% url 'school:students_level_attribution' %}" 
               hx-get="{% url 'school:students_level_attribution' %}?education=fr" 
               hx-target="#app-content"
               hx-push-url="{% url 'school:students_level_attribution' %}?education=fr">
            <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
            Attribution des classes</a>
          </li>
          {% if perms.school.view_level %}
            <div class="dropdown-divider"></div>
            <a class="treeview-item close-on-click" 
              href="{% url 'school:students_level_list' %}?education=fr" rel="noopener"
              hx-get="{% url 'school:students_level_list' %}?education=fr" 
              hx-target="#app-content" hx-push-url="{% url 'school:students_level_list' %}?education=fr">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
              Listes de classe</a>
            <a class="treeview-item close-on-click" 
              href="{% url 'exams:level_action' %}?education=fr&doc_type=register" rel="noopener"
              hx-get="{% url 'exams:level_action' %}?education=fr&doc_type=register" 
              hx-target="#app-content" hx-push-url="{% url 'exams:level_action' %}?education=fr&doc_type=register">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
              Listes d'appel mensuel</a>
          
            <a class="treeview-item close-on-click" 
              href="{% url 'exams:level_action' %}?education=fr&doc_type=daily_register" rel="noopener"
              hx-get="{% url 'exams:level_action' %}?education=fr&doc_type=daily_register" 
              hx-target="#app-content" hx-push-url="{% url 'exams:level_action' %}?education=fr&doc_type=daily_register">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
              Listes d'appel journalier</a>
            {% endif %}
          {% if user.school.cherifla_id %}
          <li>
            <a class="treeview-item close-on-click" href="{% url 'exams:cherifla_students' %}?exam=cepe" 
               hx-get="{% url 'exams:cherifla_students' %}?exam=cepe" 
               hx-target="#app-content"
               hx-push-url="{% url 'exams:cherifla_students' %}?exam=cepe">
            <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
            Dossiers DEC CHERIFLA</a>
          </li>
          {% endif %}
        </ul>
      </li>
      {% endif %}

      {% if user.is_authenticated %}
      <li class="treeview" data-toggle="tooltip">
        <a class="app-menu__item" href="#" data-toggle="treeview">
          <i data-feather="grid" class="feather-16 mr-2 feather"></i>
          <span class="app-menu__label">Classes et Pédagogie</span>
          <i data-feather="chevron-right" class="toggler-plus-icon" style="width: 18px; height: 18px;"></i>
        </a>
        <ul class="treeview-menu close-on-click">
          {% if perms.exams.manage_all_grades %}
            <li class="close-on-click">
              <a class="treeview-item close-on-click" href="" hx-get="{% url 'school:settings' %}?section=ecole" 
                hx-target="#app-content" hx-push-url="{% url 'school:settings' %}?section=ecole">
                <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Ecole
              </a>
            </li>
          {% endif %}
          {% if perms.school.view_level %}
          <li class="close-on-click">
            <a class="treeview-item close-on-click" href="" hx-get="{% url 'school:levels' %}?lang=fr" 
              hx-target="#app-content" hx-push-url="{% url 'school:levels' %}?lang=fr">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Classes
            </a>
          </li>
          {% endif %}
          <li class="close-on-click">
            {% with school_cycle=user.get_school.cycle %}
            {% if perms.exams.manage_all_grades %}
            <a class="treeview-item close-on-click" href="" hx-get="{% url 'exams:terms' %}?lang=fr&cycle={% if school_cycle != 'B' %}{{ school_cycle }}{% else %}P{% endif %}" 
               hx-target="#app-content" hx-push-url="{% url 'exams:terms' %}?lang=fr&cycle={% if school_cycle != 'B'%}{{ school_cycle }}{% else %}P{% endif %}">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Périodicité
            </a>
            {% if user.school.education == 'A' %}
            <a class="treeview-item close-on-click" href="" hx-get="{% url 'exams:terms' %}?lang=ar&cycle={% if school_cycle != 'B' %}{{ school_cycle }}{% else %}P{% endif %}" 
               hx-target="#app-content" hx-push-url="{% url 'exams:terms' %}?lang=ar&cycle={% if school_cycle != 'B'%}{{ school_cycle }}{% else %}P{% endif %}">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Périodicité arabe
            </a>
            {% endif %}
            <div class="dropdown-divider"></div>
            {% if user.school.cycle != CYCLE_SECONDARY %}
            <a class="treeview-item close-on-click" href="" hx-get="{% url 'exams:subjects' %}?cycle=primaire" 
               hx-target="#app-content" hx-push-url="{% url 'exams:subjects' %}?cycle=primaire">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Liste Matières Primaire
            </a>
            {% endif %}
            {% if user.school.cycle != CYCLE_PRIMARY %}
            <a class="treeview-item close-on-click" href="" hx-get="{% url 'exams:subjects' %}?cycle=secondaire" 
               hx-target="#app-content" hx-push-url="{% url 'exams:subjects' %}?cycle=secondaire">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Listes Matières Secondaire
            </a>
            {% endif %}
            <a class="treeview-item close-on-click" href="" hx-get="{% url 'exams:level_subjects' %}?lang=fr" 
               hx-target="#app-content" hx-push-url="{% url 'exams:level_subjects' %}?lang=fr">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Repartition des matières
            </a>
            {% if user.school.education == 'A' %}
            <a class="treeview-item close-on-click" href="" hx-get="{% url 'exams:level_subjects' %}?lang=ar" 
               hx-target="#app-content" hx-push-url="{% url 'exams:level_subjects' %}?lang=ar">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Repartition matières arabe
            </a>
            {% endif %}
            {% endif %}
            {% endwith %}

          </li>
        </ul>
      </li>

      <!-- Results -->
      {% if perms.exams.manage_all_grades %}
      <li class="treeview">
        <a class="app-menu__item" href="#" data-toggle="treeview">
          <i data-feather="copy" class="feather-16 mr-2 feather"></i>
          <span class="app-menu__label">Notes et Moyennes</span>
          <i data-feather="chevron-right" class="toggler-plus-icon" style="width: 18px; height: 18px;"></i>
        </a>
        <ul class="treeview-menu close-on-click">
          <li class="close-on-click">
            {% if user.education != 'A' %}
                <a class="treeview-item" href="{% url 'exams:level_action' %}?doc_type=marking-sheet&education=fr" hx-get="{% url 'exams:level_action' %}?doc_type=marking-sheet&education=fr" 
                    hx-target="#app-content" hx-push-url="{% url 'exams:level_action' %}?doc_type=marking-sheet&education=fr">
                  <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Fiches de Notation
                </a>
                <a class="treeview-item" href="{% url 'exams:custom_marking_sheet' %}?lang=fr" hx-get="{% url 'exams:custom_marking_sheet' %}?lang=fr" 
                    hx-target="#app-content" hx-push-url="{% url 'exams:custom_marking_sheet' %}?lang=fr">
                  <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Fiches de Notation Perso.
                </a>
                {% if perms.exams.manage_all_grades %}
                <a class="treeview-item" href="{% url 'exams:level_action' %}?doc_type=fiche_table&education=fr" hx-get="{% url 'exams:level_action' %}?doc_type=fiche_table&education=fr" 
                   hx-target="#app-content" hx-push-url="{% url 'exams:level_action' %}?doc_type=fiche_table&education=fr">
                  <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Fiches de Table
                </a>
                {% endif %}
                <div class="dropdown-divider"></div>
                <a class="treeview-item" href="{% url 'exams:grades' %}?type=tout&lang=F" hx-get="{% url 'exams:grades' %}?type=tout&lang=F" 
                  hx-target="#app-content" hx-push-url="{% url 'exams:grades' %}?type=tout&lang=F">
                  <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Moyennes {% if user.school.education == 'A' %} Français {% endif %}
                </a>
                {% if user.school.cycle != 'S' %}
                <!-- <a class="treeview-item" href="{% url 'exams:results_by_cycle' %}?type=primaire&lang=F" hx-get="{% url 'exams:results_by_cycle' %}?type=primaire&lang=F" 
                  hx-target="#app-content" hx-push-url="{% url 'exams:results_by_cycle' %}?type=primaire&lang=F">
                  <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Résultats & Bulletins
                </a> -->
                <a class="treeview-item" href="{% url 'exams:results_by_cycle' %}?type=primaire&lang=F&option=resultats" 
                  hx-get="{% url 'exams:results_by_cycle' %}?type=primaire&lang=F&option=resultats" 
                  hx-target="#app-content" hx-push-url="{% url 'exams:results_by_cycle' %}?type=primaire&lang=F&option=resultats">
                  <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Résultats
                </a>
                <a class="treeview-item" href="{% url 'exams:results' %}?type=periode&lang=F" 
                  hx-get="{% url 'exams:results' %}?type=periode&lang=F" 
                  hx-target="#app-content" hx-push-url="{% url 'exams:results' %}?type=periode&lang=F">
                  <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Bulletins par élève
                </a>
                <a class="treeview-item" href="{% url 'exams:results_by_cycle' %}?type=primaire&lang=F&option=bulletins" 
                  hx-get="{% url 'exams:results_by_cycle' %}?type=primaire&lang=F&option=bulletins" 
                  hx-target="#app-content" hx-push-url="{% url 'exams:results_by_cycle' %}?type=primaire&lang=F&option=bulletins">
                  <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Bulletins par classe
                </a>
                {% else %}
                <!-- <a class="treeview-item" href="{% url 'exams:results_by_cycle' %}?type=secondaire&lang=F" hx-get="{% url 'exams:results_by_cycle' %}?type=secondaire&lang=F" 
                  hx-target="#app-content" hx-push-url="{% url 'exams:results_by_cycle' %}?type=secondaire&lang=F">
                  <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Résultats & Bulletins
                </a> -->
                <a class="treeview-item" href="{% url 'exams:results_by_cycle' %}?type=secondaire&lang=F&option=resultats" hx-get="{% url 'exams:results_by_cycle' %}?type=secondaire&lang=F&option=resultats" 
                  hx-target="#app-content" hx-push-url="{% url 'exams:results_by_cycle' %}?type=secondaire&lang=F&option=resultats">
                  <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Résultats
                </a>
                <a class="treeview-item" href="{% url 'exams:results' %}?type=periode&lang=F" 
                  hx-get="{% url 'exams:results' %}?type=periode&lang=F" 
                  hx-target="#app-content" hx-push-url="{% url 'exams:results' %}?type=periode&lang=F">
                  <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Bulletins par élève
                </a>
                <a class="treeview-item" href="{% url 'exams:results_by_cycle' %}?type=secondaire&lang=F&option=bulletins" 
                  hx-get="{% url 'exams:results_by_cycle' %}?type=secondaire&lang=F&option=bulletins" 
                  hx-target="#app-content" hx-push-url="{% url 'exams:results_by_cycle' %}?type=secondaire&lang=F&option=bulletins">
                  <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Bulletins par classe
                </a>
                {% endif %}
                <a class="treeview-item" href="{% url 'exams:dfa' %}?education=fr" hx-get="{% url 'exams:dfa' %}?education=fr" 
                hx-target="#app-content" hx-push-url="{% url 'exams:dfa' %}?education=fr">
                <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Moyennes Générales et DFA
              </a>
              </li>
          {% endif %}
        </ul>
      </li>

      {% endif %}
      {% if perms.exams.manage_all_grades and user.school.education == 'A' and user.education != 'F' %}
      <li class="treeview">
        <a class="app-menu__item" href="#" data-toggle="treeview">
          <i data-feather="copy" class="feather-16 mr-2 feather"></i>
          <span class="app-menu__label">Notes et Moyennes Ara.</span>
          <i data-feather="chevron-right" class="toggler-plus-icon" style="width: 18px; height: 18px;"></i>
        </a>
        <ul class="treeview-menu close-on-click">
          <li class="close-on-click">
            <a class="treeview-item" href="{% url 'exams:level_action' %}?doc_type=marking-sheet&education=ar" hx-get="{% url 'exams:level_action' %}?doc_type=marking-sheet&education=ar" 
               hx-target="#app-content" hx-push-url="{% url 'exams:level_action' %}?doc_type=marking-sheet&education=ar">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Fiches de Notation
            </a>
            <a class="treeview-item" href="{% url 'exams:custom_marking_sheet' %}?lang=ar" hx-get="{% url 'exams:custom_marking_sheet' %}?lang=ar" 
               hx-target="#app-content" hx-push-url="{% url 'exams:custom_marking_sheet' %}?lang=ar">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Fiches de Notation Perso.
            </a>
            {% if perms.exams.manage_all_grades %}
            <a class="treeview-item" href="{% url 'exams:level_action' %}?doc_type=fiche_table&education=ar" hx-get="{% url 'exams:level_action' %}?doc_type=fiche_table&education=ar" 
               hx-target="#app-content" hx-push-url="{% url 'exams:level_action' %}?doc_type=fiche_table&education=ar">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Fiches de Table
            </a>
            {% endif %}
            <div class="dropdown-divider"></div>

            <a class="treeview-item" href="{% url 'exams:grades' %}?type=tout&lang=A" hx-get="{% url 'exams:grades' %}?type=tout&lang=A" 
               hx-target="#app-content" hx-push-url="{% url 'exams:grades' %}?type=tout&lang=A">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Moyennes Arabes
            </a>
            {% if user.school.cycle != 'S' %}
            <a class="treeview-item" href="{% url 'exams:results_by_cycle' %}?type=primaire&lang=A&option=resultats" hx-get="{% url 'exams:results_by_cycle' %}?type=primaire&lang=A&option=resultats" 
               hx-target="#app-content" hx-push-url="{% url 'exams:results_by_cycle' %}?type=primaire&lang=A&option=resultats">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Résultats
            </a>
          <a class="treeview-item" href="{% url 'exams:results' %}?type=periode&lang=A" 
            hx-get="{% url 'exams:results' %}?type=periode&lang=A" 
            hx-target="#app-content" hx-push-url="{% url 'exams:results' %}?type=periode&lang=A">
            <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Bulletins par élève
          </a>
          <a class="treeview-item" href="{% url 'exams:results_by_cycle' %}?type=primaire&lang=A&option=bulletins" 
            hx-get="{% url 'exams:results_by_cycle' %}?type=primaire&lang=A&option=bulletins" 
            hx-target="#app-content" hx-push-url="{% url 'exams:results_by_cycle' %}?type=primaire&lang=A&option=bulletins">
            <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Bulletins par classe
          </a>
            {% else %}
            <a class="treeview-item" href="{% url 'exams:results_by_cycle' %}?type=secondaire&lang=A&option=resultats" hx-get="{% url 'exams:results_by_cycle' %}?type=secondaire&lang=A&option=resultats" 
               hx-target="#app-content" hx-push-url="{% url 'exams:results_by_cycle' %}?type=secondaire&lang=A&option=resultats">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Résultats
            </a>
            <a class="treeview-item" href="{% url 'exams:results' %}?type=periode&lang=A" 
            hx-get="{% url 'exams:results' %}?type=periode&lang=A" 
            hx-target="#app-content" hx-push-url="{% url 'exams:results' %}?type=periode&lang=A">
            <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Bulletins par élève
          </a>
          <a class="treeview-item" href="{% url 'exams:results_by_cycle' %}?type=secondaire&lang=A&option=bulletins" 
            hx-get="{% url 'exams:results_by_cycle' %}?type=secondaire&lang=A&option=bulletins" 
            hx-target="#app-content" hx-push-url="{% url 'exams:results_by_cycle' %}?type=secondaire&lang=A&option=bulletins">
            <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Bulletins par classe
          </a>
            {% endif %}
            <a class="treeview-item" href="{% url 'exams:dfa' %}?education=ar" hx-get="{% url 'exams:dfa' %}?education=ar" 
            hx-target="#app-content" hx-push-url="{% url 'exams:dfa' %}?education=ar">
            <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Moyennes Générales et DFA
          </a>
            <a class="treeview-item" href="{% url 'exams:certificates' %}?exam=cepe" hx-get="{% url 'exams:certificates' %}?exam=cepe" 
            hx-target="#app-content" hx-push-url="{% url 'exams:certificates' %}?exam=cepe">
            <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> Diplomes
          </a>
          </li>
        </ul>
      </li>

      {% endif %}

      {% if perms.school.view_level %}
      <li class="treeview">
        <a class="app-menu__item" href="#" data-toggle="treeview">
          <i data-feather="users" class="feather-16 mr-2 feather"></i>
          <span class="app-menu__label">Personnel de l'école 
            <span class="badge badge-info">New</span>
          </span>
          <i data-feather="chevron-right" class="toggler-plus-icon" style="width: 18px; height: 18px;"></i>
        </a>
        <ul class="treeview-menu close-on-click">
            <li class="close-on-click"><a class="treeview-item" 
              href="{% url 'users:users' %}" rel="noopener"
              hx-get="{% url 'users:users' %}" 
              hx-target="#app-content" hx-push-url="{% url 'users:users' %}">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
              Administration</a>
            </li>
            <li class="close-on-click"><a class="treeview-item" 
              href="{% url 'school:teachers' %}?education=fr" rel="noopener"
              hx-get="{% url 'school:teachers' %}?education=fr" 
              hx-target="#app-content" hx-push-url="{% url 'school:teachers' %}?education=fr">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
              Enseignants</a>
            </li>
            
            <li class="close-on-click">
                <a class="treeview-item" 
                href="{% url 'school:staff' %}" rel="noopener"
                hx-get="{% url 'school:staff' %}" 
                hx-target="#app-content" hx-push-url="{% url 'school:staff' %}">
                <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
                Gestion des salaires <span class="badge badge-info">New</span>
                </a>
            </li>
            <!-- <li class="close-on-click"><a class="treeview-item" 
              href="" rel="noopener"
              hx-get="" 
              hx-target="#app-content" hx-push-url="">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
              Parents d'élève</a>
            </li> -->
          </ul>
        </li>
        {% endif %}
      <li class="treeview">
        <a class="app-menu__item" href="#" data-toggle="treeview">
          <i data-feather="bar-chart" class="feather-16 mr-2 feather"></i>
          <span class="app-menu__label">Statistiques</span>
          <i data-feather="chevron-right" class="toggler-plus-icon" style="width: 18px; height: 18px;"></i>
        </a>
        <ul class="treeview-menu close-on-click">
          {% if perms.school.view_level %}
            <li class="close-on-click"><a class="treeview-item" 
              href="{% url 'school:levels_statistics' %}?education=fr" rel="noopener"
              hx-get="{% url 'school:levels_statistics' %}?education=fr" 
              hx-target="#app-content" hx-push-url="{% url 'school:levels_statistics' %}?education=fr">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
              Effectifs par classe et genre</a>
            </li>
          {% endif %}
        </ul>
      </li>
      <li class="treeview">
        <a class="app-menu__item" href="#" data-toggle="treeview">
          <i data-feather="upload-cloud" class="feather-16 mr-2 feather"></i>
          <span class="app-menu__label">Importations et export.</span>
          <i data-feather="chevron-right" class="toggler-plus-icon" style="width: 18px; height: 18px;"></i>
        </a>
        <ul class="treeview-menu close-on-click">
          {% if perms.school.view_student %}
            <li class="close-on-click"><a class="treeview-item" 
              href="{% url 'school:import' %}?nolist" rel="noopener"
              hx-get="{% url 'school:import' %}?nolist" 
              hx-target="#app-content" hx-push-url="{% url 'school:import' %}?nolist">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
              Elèves</a>
            </li>
          {% endif %}
        </ul>
        <ul class="treeview-menu close-on-click d-none">
          {% if perms.school.view_student %}
            <li class="close-on-click"><a class="treeview-item" 
              href="{% url 'school:levels_statistics' %}?education=fr" rel="noopener"
              hx-get="{% url 'school:levels_statistics' %}?education=fr" 
              hx-target="#app-content" hx-push-url="{% url 'school:levels_statistics' %}?education=fr">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
              Enseignants</a>
            </li>
          {% endif %}
        </ul>
        <ul class="treeview-menu close-on-click d-none">
          {% if perms.school.view_student %}
            <li class="close-on-click"><a class="treeview-item" 
              href="{% url 'school:levels_statistics' %}?education=fr" rel="noopener"
              hx-get="{% url 'school:levels_statistics' %}?education=fr" 
              hx-target="#app-content" hx-push-url="{% url 'school:levels_statistics' %}?education=fr">
              <i data-feather="corner-down-right" class="feather-11 font-weight-bold mr-2"></i> 
              Notes et Moyennes</a>
            </li>
          {% endif %}
        </ul>
      </li>
      <li><a class="app-menu__item close-on-click" 
        href="{% url 'school:sms_balance' %}" 
        hx-get="{% url 'school:sms_balance' %}" 
        hx-push-url="{% url 'school:sms_balance' %}"
        hx-target="#app-content">
        <i data-feather="message-square" class="feather-16 mr-2"></i>
        <span class="app-menu__label">Messagerie</span></a>
      </li>
     
      

      <!-- {% if perms.school.add_payment %}
      <li><a class="app-menu__item close-on-click" 
        href="{% url 'school:import' %}" 
        hx-get="{% url 'school:import' %}" 
        hx-push-url="{% url 'school:import' %}"
        hx-target="#app-content">
        <i data-feather="corner-left-up" class="feather-16 mr-2"></i>
        <span class="app-menu__label">Importations</span></a>
      </li>
      {% endif %} -->

      {% endif %}
      {% endif %}
    </ul>
  {% else %}
    {% include 'partials/unique_user/unique_user_sidenav.html' %}
  {% endif %}
  </aside>