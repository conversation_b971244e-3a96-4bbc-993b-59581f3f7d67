{% load widget_tweaks %}
{% load humanize %}

<div method="post" action="" class="dashboard-wrapper"
     hx-include="this"
     hx-get="{{ request.path }}?page={{ page }}{% if education %}&education={{ education}}{% endif %}"
     hx-trigger="saved from:body"
     hx-target="#app-content">

  <!-- Filters Section -->
  {% include 'material/students/filter_section.html' %}

  <!-- Material Design Data Table -->
  <div class="mdc-card data-table-card mdc-card--outlined">
    <!-- Data Table Header with Actions -->
    <div class="data-table-header">
        <!-- Filter Toolbar -->
        <div class="filter-toolbar">
            <button class="mdc-icon-button" aria-label="Filter">
                <div class="mdc-icon-button__ripple"></div>
                <span class="material-icons">filter_list</span>
            </button>
        </div>

        <!-- Data Table Actions -->
        <div class="data-table-actions">
            <!-- Search Field -->
            <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon search-field">
                <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">search</span>
                <input id="data-table-search" class="mdc-text-field__input" type="text"
                       name="search"
                       placeholder="Rechercher par nom ou matricule..."
                       value="{{ search|default:'' }}"
                       hx-get="{{ request.path }}"
                       hx-target="#app-content"
                       hx-trigger="keyup changed delay:300ms">
                <div class="mdc-notched-outline">
                    <div class="mdc-notched-outline__leading"></div>
                    <div class="mdc-notched-outline__notch">
                        <label class="mdc-floating-label">Rechercher</label>
                    </div>
                    <div class="mdc-notched-outline__trailing"></div>
                </div>
            </div>

            <!-- More Options Button -->
            <button class="mdc-icon-button" aria-label="More options">
                <div class="mdc-icon-button__ripple"></div>
                <span class="material-icons">more_vert</span>
            </button>
        </div>
    </div>

    <!-- Data Table -->
    <div class="mdc-data-table">
        <div class="mdc-data-table__table-container">
            <table class="mdc-data-table__table">
                <thead>
                    <tr class="mdc-data-table__header-row">
                        <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox">
                            <div class="mdc-checkbox mdc-data-table__header-row-checkbox">
                                <input type="checkbox" class="mdc-checkbox__native-control"
                                       id="select-all" aria-label="Toggle all rows"/>
                                <div class="mdc-checkbox__background">
                                    <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                        <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                    </svg>
                                    <div class="mdc-checkbox__mixedmark"></div>
                                </div>
                            </div>
                        </th>
                        <th class="mdc-data-table__header-cell" style="width: 60px">Photo</th>
                        <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort sticky-column"
                            role="columnheader" scope="col" aria-sort="none"
                            hx-get="{{ request.path }}"
                            hx-include="[name='per_page'], [name='search']"
                            hx-vals='{"sort": "{% if sort_field == "student__last_name" and sort_direction == "asc" %}-{% endif %}student__last_name"}'
                            hx-target="#app-content">
                            <div class="mdc-data-table__header-cell-wrapper">
                                <div class="mdc-data-table__header-cell-label">Nom et Prénoms</div>
                                <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button"
                                        aria-label="Sort by Name">
                                    {% if sort_field == 'student__last_name' %}
                                        {% if sort_direction == 'asc' %}arrow_upward{% else %}arrow_downward{% endif %}
                                    {% else %}
                                        arrow_upward
                                    {% endif %}
                                </button>
                            </div>
                        </th>
                        <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort"
                            role="columnheader" scope="col" aria-sort="none"
                            hx-get="{{ request.path }}"
                            hx-include="[name='per_page'], [name='search']"
                            hx-vals='{"sort": "{% if sort_field == "student__student_id" and sort_direction == "asc" %}-{% endif %}student__student_id"}'
                            hx-target="#app-content">
                            <div class="mdc-data-table__header-cell-wrapper">
                                <div class="mdc-data-table__header-cell-label">Matricule</div>
                                <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button"
                                        aria-label="Sort by ID">
                                    {% if sort_field == 'student__student_id' %}
                                        {% if sort_direction == 'asc' %}arrow_upward{% else %}arrow_downward{% endif %}
                                    {% else %}
                                        arrow_upward
                                    {% endif %}
                                </button>
                            </div>
                        </th>
                        <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort"
                            role="columnheader" scope="col" aria-sort="none"
                            hx-get="{{ request.path }}"
                            hx-include="[name='per_page'], [name='search']"
                            hx-vals='{"sort": "{% if sort_field == "level_fr" and sort_direction == "asc" %}-{% endif %}level_fr"}'
                            hx-target="#app-content">
                            <div class="mdc-data-table__header-cell-wrapper">
                                <div class="mdc-data-table__header-cell-label">Classe</div>
                                <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button"
                                        aria-label="Sort by Class">
                                    {% if sort_field == 'level_fr' %}
                                        {% if sort_direction == 'asc' %}arrow_upward{% else %}arrow_downward{% endif %}
                                    {% else %}
                                        arrow_upward
                                    {% endif %}
                                </button>
                            </div>
                        </th>
                        {% if is_arabic_school %}
                        <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort"
                            role="columnheader" scope="col" aria-sort="none"
                            hx-get="{{ request.path }}"
                            hx-include="[name='per_page'], [name='search']"
                            hx-vals='{"sort": "{% if sort_field == "level_ar" and sort_direction == "asc" %}-{% endif %}level_ar"}'
                            hx-target="#app-content">
                            <div class="mdc-data-table__header-cell-wrapper">
                                <div class="mdc-data-table__header-cell-label">Arabe</div>
                                <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button"
                                        aria-label="Sort by Arabic Class">
                                    {% if sort_field == 'level_ar' %}
                                        {% if sort_direction == 'asc' %}arrow_upward{% else %}arrow_downward{% endif %}
                                    {% else %}
                                        arrow_upward
                                    {% endif %}
                                </button>
                            </div>
                        </th>
                        {% endif %}
                        <th class="mdc-data-table__header-cell">Sexe</th>
                        <th class="mdc-data-table__header-cell">Statut</th>
                        <th class="mdc-data-table__header-cell mdc-data-table__header-cell--numeric mdc-data-table__header-cell--with-sort"
                            role="columnheader" scope="col" aria-sort="none"
                            hx-get="{{ request.path }}"
                            hx-include="[name='per_page'], [name='search']"
                            hx-vals='{"sort": "{% if sort_field == "debt" and sort_direction == "asc" %}-{% endif %}debt"}'
                            hx-target="#app-content">
                            <div class="mdc-data-table__header-cell-wrapper">
                                <div class="mdc-data-table__header-cell-label">Arriéré</div>
                                <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button"
                                        aria-label="Sort by Debt">
                                    {% if sort_field == 'debt' %}
                                        {% if sort_direction == 'asc' %}arrow_upward{% else %}arrow_downward{% endif %}
                                    {% else %}
                                        arrow_upward
                                    {% endif %}
                                </button>
                            </div>
                        </th>
                        <th class="mdc-data-table__header-cell mdc-data-table__header-cell--numeric mdc-data-table__header-cell--with-sort"
                            role="columnheader" scope="col" aria-sort="none"
                            hx-get="{{ request.path }}"
                            hx-include="[name='per_page'], [name='search']"
                            hx-vals='{"sort": "{% if sort_field == "amount" and sort_direction == "asc" %}-{% endif %}amount"}'
                            hx-target="#app-content">
                            <div class="mdc-data-table__header-cell-wrapper">
                                <div class="mdc-data-table__header-cell-label">À payer</div>
                                <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button"
                                        aria-label="Sort by Amount">
                                    {% if sort_field == 'amount' %}
                                        {% if sort_direction == 'asc' %}arrow_upward{% else %}arrow_downward{% endif %}
                                    {% else %}
                                        arrow_upward
                                    {% endif %}
                                </button>
                            </div>
                        </th>
                        <th class="mdc-data-table__header-cell mdc-data-table__header-cell--numeric mdc-data-table__header-cell--with-sort"
                            role="columnheader" scope="col" aria-sort="none"
                            hx-get="{{ request.path }}"
                            hx-include="[name='per_page'], [name='search']"
                            hx-vals='{"sort": "{% if sort_field == "paid" and sort_direction == "asc" %}-{% endif %}paid"}'
                            hx-target="#app-content">
                            <div class="mdc-data-table__header-cell-wrapper">
                                <div class="mdc-data-table__header-cell-label">Payé</div>
                                <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button"
                                        aria-label="Sort by Paid">
                                    {% if sort_field == 'paid' %}
                                        {% if sort_direction == 'asc' %}arrow_upward{% else %}arrow_downward{% endif %}
                                    {% else %}
                                        arrow_upward
                                    {% endif %}
                                </button>
                            </div>
                        </th>
                        <th class="mdc-data-table__header-cell mdc-data-table__header-cell--numeric mdc-data-table__header-cell--with-sort"
                            role="columnheader" scope="col" aria-sort="none"
                            hx-get="{{ request.path }}"
                            hx-include="[name='per_page'], [name='search']"
                            hx-vals='{"sort": "{% if sort_field == "remaining" and sort_direction == "asc" %}-{% endif %}remaining"}'
                            hx-target="#app-content">
                            <div class="mdc-data-table__header-cell-wrapper">
                                <div class="mdc-data-table__header-cell-label">Reste</div>
                                <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button"
                                        aria-label="Sort by Remaining">
                                    {% if sort_field == 'remaining' %}
                                        {% if sort_direction == 'asc' %}arrow_upward{% else %}arrow_downward{% endif %}
                                    {% else %}
                                        arrow_upward
                                    {% endif %}
                                </button>
                            </div>
                        </th>
                        <th class="mdc-data-table__header-cell" style="width: 120px">Actions</th>
                    </tr>
                </thead>
                <tbody class="mdc-data-table__content">
                    {% for enrollment in enrollments %}
                    <tr class="mdc-data-table__row student-row {% if enrollment.selected %}mdc-data-table__row--selected{% endif %}"
                        data-row-id="{{ enrollment.id }}">

                        <!-- Checkbox Column -->
                        <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                            <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                <input type="checkbox" class="mdc-checkbox__native-control row-checkbox"
                                       name="check-{{ enrollment.id }}"
                                       id="check-{{ enrollment.id }}"
                                       {% if enrollment.selected %}checked="checked"{% endif %}
                                       aria-labelledby="u{{ forloop.counter0 }}"/>
                                <div class="mdc-checkbox__background">
                                    <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                        <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                    </svg>
                                    <div class="mdc-checkbox__mixedmark"></div>
                                </div>
                            </div>
                        </td>

                        <!-- Photo Column -->
                        <td class="mdc-data-table__cell">
                            <div class="student-photo-container">
                                {% if enrollment.student.photo %}
                                    <img data-original="{{ enrollment.student.photo.url }}"
                                         alt="Photo"
                                         class="student-photo lazy mdc-elevation--z1">
                                {% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}
                                    <img data-original="{{ enrollment.student.government_photo }}"
                                         alt="Photo"
                                         class="student-photo lazy mdc-elevation--z1"
                                         id="{{ enrollment.id }}"
                                         onload="if (this.src.endsWith('CC')) {
                                           this.src = '{{ enrollment.student.blank_photo }}'
                                         }">
                                {% else %}
                                    <img data-original="{{ enrollment.student.blank_photo }}"
                                         alt="Photo"
                                         class="student-photo lazy mdc-elevation--z1">
                                {% endif %}
                            </div>
                        </td>

                        <!-- Name Column (Sticky) -->
                        <td class="mdc-data-table__cell sticky-column">
                            <div class="student-name-container">
                                <div class="student-name-primary">{{ enrollment.student.get_full_name }}</div>
                                {% if is_arabic_school and enrollment.student.full_name_ar %}
                                <div class="student-name-secondary">{{ enrollment.student.full_name_ar }}</div>
                                {% endif %}
                            </div>
                        </td>

                        <!-- Student ID Column -->
                        <td class="mdc-data-table__cell">
                            <span class="student-id">{{ enrollment.student.student_id|default:"-" }}</span>
                        </td>

                        <!-- Class (French) Column -->
                        <td class="mdc-data-table__cell">
                            {% if enrollment.level_fr or enrollment.generic_level_fr %}
                                <span class="class-badge">{{ enrollment.level_fr|default:enrollment.generic_level_fr }}</span>
                            {% elif not enrollment.level_fr %}
                                <span class="class-unassigned" title="Classe non attribuée">
                                    <span class="material-icons">help_outline</span>
                                </span>
                            {% else %}
                                -
                            {% endif %}
                        </td>

                        <!-- Class (Arabic) Column -->
                        {% if is_arabic_school %}
                        <td class="mdc-data-table__cell">
                            {% if enrollment.level_ar or enrollment.generic_level_ar %}
                                <span class="class-badge">{{ enrollment.level_ar|default:enrollment.generic_level_ar }}</span>
                            {% elif not enrollment.level_ar %}
                                <span class="class-unassigned" title="Classe arabe non attribuée">
                                    <span class="material-icons">help_outline</span>
                                </span>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        {% endif %}

                        <!-- Gender Column -->
                        <td class="mdc-data-table__cell">
                            <span class="gender-badge">{{ enrollment.student.gender }}</span>
                        </td>

                        <!-- Status Column -->
                        <td class="mdc-data-table__cell">
                            {% if enrollment.status %}
                            <div class="mdc-chip status-chip {% if enrollment.active %}status-active{% else %}status-inactive{% endif %}">
                                <div class="mdc-chip__ripple"></div>
                                <span class="mdc-chip__text">{{ enrollment.status }}</span>
                            </div>
                            {% else %}
                            -
                            {% endif %}
                        </td>

                        <!-- Debt Column -->
                        <td class="mdc-data-table__cell mdc-data-table__cell--numeric">
                            <span class="amount-cell amount-negative">
                                {{ enrollment.debt|intcomma|default:"0" }}
                            </span>
                        </td>

                        <!-- Amount to Pay Column -->
                        <td class="mdc-data-table__cell mdc-data-table__cell--numeric">
                            <span class="amount-cell">
                                {{ enrollment.amount|intcomma }}
                            </span>
                        </td>

                        <!-- Paid Column -->
                        <td class="mdc-data-table__cell mdc-data-table__cell--numeric">
                            <span class="amount-cell {% if enrollment.paid %}amount-positive{% endif %}">
                                {% if enrollment.paid %}{{ enrollment.paid|intcomma|default:"0" }}{% else %}-{% endif %}
                            </span>
                        </td>

                        <!-- Remaining Column -->
                        <td class="mdc-data-table__cell mdc-data-table__cell--numeric">
                            <span class="amount-cell {% if enrollment.remaining == 0 %}amount-positive{% elif enrollment.remaining > 0 %}amount-warning{% endif %}">
                                {% if enrollment.remaining == 0 and enrollment.get_fees_total > 0 %}
                                    Soldé
                                {% else %}
                                    {{ enrollment.remaining|intcomma|default:"-" }}
                                {% endif %}
                            </span>
                        </td>

                        <!-- Actions Column -->
                        <td class="mdc-data-table__cell">
                            <div class="mdc-data-table__row-actions">
                                {% if enrollment.active %}
                                    <!-- Edit Button -->
                                    <button class="mdc-icon-button action-button show-on-pc"
                                            aria-label="Modifier infos"
                                            hx-get="{% url 'school:student_edit' enrollment.id %}?page={{ page }}"
                                            hx-target="#dialog-xl">
                                        <div class="mdc-icon-button__ripple"></div>
                                        <span class="material-icons">edit</span>
                                    </button>

                                    <!-- Edit Button Mobile -->
                                    <button class="mdc-icon-button action-button show-on-phone"
                                            aria-label="Modifier infos"
                                            hx-get="{% url 'school:student_edit_wizard' enrollment.id %}?page={{ page }}"
                                            hx-target="#dialog">
                                        <div class="mdc-icon-button__ripple"></div>
                                        <span class="material-icons">edit</span>
                                    </button>

                                    <!-- Payment Button -->
                                    <button class="mdc-icon-button action-button"
                                            aria-label="Ajouter/Modifier un paiement"
                                            hx-get="{% url 'school:payment_add' %}?student_id={{ enrollment.student.identifier }}"
                                            hx-target="#dialog-xl">
                                        <div class="mdc-icon-button__ripple"></div>
                                        <span class="material-icons">attach_money</span>
                                    </button>

                                    <!-- More Actions Menu -->
                                    <div class="mdc-menu-surface--anchor">
                                        <button class="mdc-icon-button action-button"
                                                aria-label="Plus d'actions"
                                                data-mdc-menu-button>
                                            <div class="mdc-icon-button__ripple"></div>
                                            <span class="material-icons">more_vert</span>
                                        </button>

                                        <div class="mdc-menu mdc-menu-surface">
                                            <ul class="mdc-list" role="menu" aria-hidden="true">
                                                <li class="mdc-list-item" role="menuitem">
                                                    <span class="mdc-list-item__ripple"></span>
                                                    <a href="{% url 'school:student_payments_pdf' enrollment.id %}?template=2&copies=1"
                                                       target="_blank" class="mdc-list-item__text">
                                                        Reçu Modèle 1
                                                    </a>
                                                </li>
                                                <li class="mdc-list-item" role="menuitem">
                                                    <span class="mdc-list-item__ripple"></span>
                                                    <a href="{% url 'school:student_payments_pdf' enrollment.id %}?template=1&copies=1"
                                                       target="_blank" class="mdc-list-item__text">
                                                        Reçu Modèle 2
                                                    </a>
                                                </li>
                                                <li class="mdc-list-divider" role="separator"></li>
                                                <li class="mdc-list-item" role="menuitem">
                                                    <span class="mdc-list-item__ripple"></span>
                                                    <span class="mdc-list-item__text"
                                                          hx-get="{% url 'school:student_delete' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                                                          hx-target="#dialog">
                                                        Supprimer élève
                                                    </span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- Delete Button (Desktop) -->
                                    <button class="mdc-icon-button action-button show-on-pc"
                                            aria-label="Supprimer élève"
                                            hx-get="{% url 'school:student_delete' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                                            hx-target="#dialog">
                                        <div class="mdc-icon-button__ripple"></div>
                                        <span class="material-icons">delete</span>
                                    </button>

                                {% else %}
                                    <!-- Enroll Button -->
                                    <button class="mdc-button mdc-button--raised action-button show-on-pc"
                                            hx-get="{% url 'school:student_edit' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                                            hx-target="#dialog-xl">
                                        <div class="mdc-button__ripple"></div>
                                        <span class="material-icons mdc-button__icon">person_add</span>
                                        <span class="mdc-button__label">Inscrire</span>
                                    </button>

                                    <button class="mdc-button mdc-button--raised action-button show-on-phone"
                                            hx-get="{% url 'school:student_edit_wizard' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                                            hx-target="#dialog">
                                        <div class="mdc-button__ripple"></div>
                                        <span class="material-icons mdc-button__icon">person_add</span>
                                        <span class="mdc-button__label">Inscrire</span>
                                    </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Data Table Pagination -->
        <div class="mdc-data-table__pagination">
            <div class="mdc-data-table__pagination-trailing">
                <div class="mdc-data-table__pagination-rows-per-page">
                    <div class="mdc-data-table__pagination-rows-per-page-label">
                        Lignes par page
                    </div>

                    <div class="mdc-select mdc-select--outlined mdc-select--no-label mdc-data-table__pagination-rows-per-page-select">
                        <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                            <span class="mdc-select__selected-text">{{ per_page|default:"10" }}</span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <span class="mdc-notched-outline">
                                <span class="mdc-notched-outline__leading"></span>
                                <span class="mdc-notched-outline__trailing"></span>
                            </span>
                        </div>

                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-list" role="listbox">
                                <li class="mdc-list-item {% if per_page == '10' %}mdc-list-item--selected{% endif %}"
                                    aria-selected="{% if per_page == '10' %}true{% else %}false{% endif %}"
                                    role="option" data-value="10"
                                    hx-get="{{ request.path }}"
                                    hx-target="#app-content"
                                    hx-include="[name='search']"
                                    hx-vals='{"per_page": "10"}'>
                                    <span class="mdc-list-item__text">10</span>
                                </li>
                                <li class="mdc-list-item {% if per_page == '25' %}mdc-list-item--selected{% endif %}"
                                    aria-selected="{% if per_page == '25' %}true{% else %}false{% endif %}"
                                    role="option" data-value="25"
                                    hx-get="{{ request.path }}"
                                    hx-target="#app-content"
                                    hx-include="[name='search']"
                                    hx-vals='{"per_page": "25"}'>
                                    <span class="mdc-list-item__text">25</span>
                                </li>
                                <li class="mdc-list-item {% if per_page == '50' %}mdc-list-item--selected{% endif %}"
                                    aria-selected="{% if per_page == '50' %}true{% else %}false{% endif %}"
                                    role="option" data-value="50"
                                    hx-get="{{ request.path }}"
                                    hx-target="#app-content"
                                    hx-include="[name='search']"
                                    hx-vals='{"per_page": "50"}'>
                                    <span class="mdc-list-item__text">50</span>
                                </li>
                                <li class="mdc-list-item {% if per_page == '100' %}mdc-list-item--selected{% endif %}"
                                    aria-selected="{% if per_page == '100' %}true{% else %}false{% endif %}"
                                    role="option" data-value="100"
                                    hx-get="{{ request.path }}"
                                    hx-target="#app-content"
                                    hx-include="[name='search']"
                                    hx-vals='{"per_page": "100"}'>
                                    <span class="mdc-list-item__text">100</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="mdc-data-table__pagination-navigation">
                    {% if page_obj %}
                    <div class="mdc-data-table__pagination-total">
                        {{ page_obj.start_index }}-{{ page_obj.end_index }} de {{ page_obj.paginator.count }}
                    </div>
                    <button class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                            data-page="first" {% if not page_obj.has_previous %}disabled{% endif %}
                            {% if page_obj.has_previous %}
                                hx-get="{{ request.path }}"
                                hx-target="#app-content"
                                hx-include="[name='search'], [name='per_page']"
                                hx-vals='{"page": "1"}'
                            {% endif %}>
                        <div class="mdc-button__icon">first_page</div>
                    </button>
                    <button class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                            data-page="prev" {% if not page_obj.has_previous %}disabled{% endif %}
                            {% if page_obj.has_previous %}
                                hx-get="{{ request.path }}"
                                hx-target="#app-content"
                                hx-include="[name='search'], [name='per_page']"
                                hx-vals='{"page": "{{ page_obj.previous_page_number }}"}'
                            {% endif %}>
                        <div class="mdc-button__icon">chevron_left</div>
                    </button>
                    <button class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                            data-page="next" {% if not page_obj.has_next %}disabled{% endif %}
                            {% if page_obj.has_next %}
                                hx-get="{{ request.path }}"
                                hx-target="#app-content"
                                hx-include="[name='search'], [name='per_page']"
                                hx-vals='{"page": "{{ page_obj.next_page_number }}"}'
                            {% endif %}>
                        <div class="mdc-button__icon">chevron_right</div>
                    </button>
                    <button class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                            data-page="last" {% if not page_obj.has_next %}disabled{% endif %}
                            {% if page_obj.has_next %}
                                hx-get="{{ request.path }}"
                                hx-target="#app-content"
                                hx-include="[name='search'], [name='per_page']"
                                hx-vals='{"page": "{{ page_obj.paginator.num_pages }}"}'
                            {% endif %}>
                        <div class="mdc-button__icon">last_page</div>
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
  </div>

  <!-- Include actions template -->
  {% include 'partials/student/actions.html' %}
</div>

<!-- Include JavaScript -->
{% include 'material/students/students_list_js.html' %}