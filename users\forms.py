from django import forms
from django.core.validators import RegexValidator
from users import models


class UserForm(forms.ModelForm):
    teacher_id = forms.CharField(max_length=4, required=False)
    class Meta:
        model = models.CustomUser
        fields = [
            'last_name', 'first_name',
            'role', 'education', 'phone', 'custom_password',
            'teacher_id'
        ]


class PhoneVerificationForm(forms.Form):
    """
    Form for entering phone number for password reset.
    """
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Le numéro de téléphone doit être au format: '+999999999'. 10 à 15 chiffres autorisés."
    )
    phone = forms.CharField(
        validators=[phone_regex],
        max_length=17,
        required=True,
        label="Numéro de téléphone",
        widget=forms.TextInput(attrs={'placeholder': 'Ex: +22507XXXXXXXX'})
    )


class VerifyCodeForm(forms.Form):
    """
    Form for verifying the SMS code.
    """
    code = forms.CharField(
        max_length=6,
        min_length=6,
        required=True,
        label="Code de vérification",
        widget=forms.TextInput(attrs={'placeholder': 'Entrez le code à 6 chiffres'})
    )

    def clean_code(self):
        code = self.cleaned_data.get('code')
        if not code.isdigit():
            raise forms.ValidationError("Le code doit contenir uniquement des chiffres.")
        return code