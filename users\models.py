from datetime import datetime, timedelta
import random
import string
from django.db import models
from django.templatetags.static import static
from django.contrib.auth.models import AbstractUser
from django.contrib.auth.models import UserManager
from django.db.models import Q, F, ExpressionWrapper, fields
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.conf import settings
from cloudinary.models import CloudinaryField
from school.models import School, Year, Subscription
from school.school_utils import get_current_year
from main import utils

class CustomUserManager(models.Manager):
    def get_all(self, user, year=None, show_for_payments=False):
        queryset = super().get_queryset().filter(school=user.school)
        role = user.role
        if show_for_payments:
            queryset = queryset.filter(
                Q(role=utils.ROLE_ACCOUNTANT) | Q(role=utils.ROLE_FOUNDER)
            )
        elif role == utils.ROLE_FOUNDER:
            queryset = queryset.exclude(
                Q(role=utils.ROLE_FOUNDER) | Q(role=utils.ROLE_TEACHER))
        elif role == utils.ROLE_COMPUTER_SCIENTIST:
            if not year:
                year = get_current_year()
            queryset = queryset.filter(
                Q(role=utils.ROLE_TEACHER), Q(year=year))
        else:
            queryset = queryset.filter(id=user.id)
        return queryset.order_by('last_name', 'first_name')


class CustomUser(AbstractUser):
    ROLE_CHOICES = (
        (utils.ROLE_ACCOUNTANT, _('Comptable')),
        (utils.ROLE_DIRECTOR, _('Directeur')),
        (utils.ROLE_FOUNDER, _('Administrateur')),
        (utils.ROLE_TEACHER, _('Enseignant')),
        (utils.ROLE_COMPUTER_SCIENTIST, _('Informaticien')),
    )
    education = models.CharField(
        max_length=1, choices=utils.EDUCATION_CHOICES,
        null=True, blank=True)
    phone = models.CharField(max_length=255, null=True)
    school = models.ForeignKey(School, on_delete=models.CASCADE,
             verbose_name=_('school'), null=True, blank=True)
    role = models.CharField(max_length=2, choices=ROLE_CHOICES,
           default=utils.ROLE_FOUNDER)
    custom_password = models.CharField(_('mot de passe'),
        max_length=255, null=True, blank=True)
    year = models.ForeignKey(Year, on_delete=models.PROTECT,
           null=True, blank=True)
    photo = CloudinaryField("photo d'identité", folder='school/users_photos/',
            null=True, blank=True, transformation={
                **utils.CLOUDINARY_TRANFORMATIONS
            })
    objects = UserManager()
    for_user = CustomUserManager()

    class Meta:
        permissions = [
            ('can_import_students', 'Can import students using an Excel file',),
        ]
        verbose_name = 'utilisateur'

    def get_school(self):
        return School.objects.filter(pk=self.school_id).only('education', 'cycle').first()

    def get_school_accountant(self):
        if self.get_school().customuser_set.filter(role=utils.ROLE_ACCOUNTANT).exists():
            return self.get_school().customuser_set \
                .filter(role=utils.ROLE_ACCOUNTANT) \
                .first()
        return self

    def get_school_accountant_name(self):
        acc = self.get_school_accountant()
        if acc:
            return acc.get_full_name()
        return self.get_full_name()

    def __str__(self):
        return self.last_name

    def get_full_name(self):
        return f'{self.last_name.upper()} {self.first_name.upper()}'

    def get_photo(self):
        if self.photo:
            return self.photo.url
        return static('img/profile_pic.png')

    def get_password_chars(self):
        return f"{'* ' * len(self.custom_password)}"

    def get_school_subscription(self, school_id, year_id):
        return Subscription.objects.filter(
            school__id=school_id, year__name=year_id) \
            .annotate(days_elapsed=ExpressionWrapper(
                timezone.now() - F('created_at'),
                output_field=fields.DurationField()
            )).only('plan', 'plan_type').first()

    def has_active_subscription(self, school_id, year_id):
        obj = self.get_school_subscription(school_id=school_id, year_id=year_id)

        if obj:
            plan_type = obj.plan_type
            return (plan_type == Subscription.PLAN_TYPE_PAID) or \
                    (plan_type == Subscription.PLAN_TYPE_TEST and \
                     obj.days_elapsed.days <= settings.TEST_ACCOUNT_DAYS)
        return False


class VerificationCode(models.Model):
    """
    Model to store verification codes for password reset.
    """
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='verification_codes')
    code = models.CharField(max_length=6)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)

    class Meta:
        verbose_name = 'Code de vérification'
        verbose_name_plural = 'Codes de vérification'

    def __str__(self):
        return f"Code for {self.user.get_full_name()}: {self.code}"

    def is_valid(self):
        """Check if the verification code is still valid."""
        return not self.is_used and timezone.now() < self.expires_at

    @classmethod
    def generate_code(cls, user, expiry_minutes=15):
        """Generate a new verification code for the user."""
        # Delete any existing unused codes for this user
        cls.objects.filter(user=user, is_used=False).delete()

        # Generate a random 6-digit code
        code = ''.join(random.choices(string.digits, k=6))

        # Calculate expiry time
        expires_at = timezone.now() + timedelta(minutes=expiry_minutes)

        # Create and return the new verification code
        return cls.objects.create(
            user=user,
            code=code,
            expires_at=expires_at
        )