<script>
document.addEventListener('DOMContentLoaded', function() {
  initializeMaterialStudentsTable();
});

// Re-initialize after HTMX requests
document.body.addEventListener('htmx:afterSwap', function() {
  initializeMaterialStudentsTable();
});

function initializeMaterialStudentsTable() {
  // Initialize Material Design components
  initializeMaterialComponents();
  
  // Initialize lazy loading for student photos
  initializeLazyLoading();
  
  // Initialize checkbox functionality
  initializeCheckboxes();
  
  // Initialize row click functionality
  initializeRowClicks();
  
  // Initialize filters
  initializeFilters();
  
  // Initialize action menus
  initializeActionMenus();
}

function initializeMaterialComponents() {
  // Initialize all Material Design components
  if (typeof mdc !== 'undefined') {
    // Initialize data table
    const dataTable = document.querySelector('.mdc-data-table');
    if (dataTable && !dataTable.mdcDataTable) {
      dataTable.mdcDataTable = new mdc.dataTable.MDCDataTable(dataTable);
    }
    
    // Initialize checkboxes
    document.querySelectorAll('.mdc-checkbox').forEach(checkbox => {
      if (!checkbox.mdcCheckbox) {
        checkbox.mdcCheckbox = new mdc.checkbox.MDCCheckbox(checkbox);
      }
    });
    
    // Initialize buttons with ripple effect
    document.querySelectorAll('.mdc-button, .mdc-icon-button').forEach(button => {
      if (!button.mdcRipple) {
        button.mdcRipple = new mdc.ripple.MDCRipple(button);
      }
    });
    
    // Initialize chips
    document.querySelectorAll('.mdc-chip').forEach(chip => {
      if (!chip.mdcChip) {
        chip.mdcChip = new mdc.chips.MDCChip(chip);
      }
    });
    
    // Initialize text fields
    document.querySelectorAll('.mdc-text-field').forEach(textField => {
      if (!textField.mdcTextField) {
        textField.mdcTextField = new mdc.textField.MDCTextField(textField);
      }
    });
    
    // Initialize select components
    document.querySelectorAll('.mdc-select').forEach(select => {
      if (!select.mdcSelect) {
        select.mdcSelect = new mdc.select.MDCSelect(select);
      }
    });
  }
}

function initializeLazyLoading() {
  // Initialize lazy loading for student photos
  if (typeof $ !== 'undefined' && $.fn.lazyload) {
    $("img.lazy").lazyload({
      container: $(".mdc-data-table__table-container")
    });
  }
}

function initializeCheckboxes() {
  const selectAllCheckbox = document.getElementById('select-all');
  const rowCheckboxes = document.querySelectorAll('.row-checkbox');
  
  if (selectAllCheckbox) {
    selectAllCheckbox.addEventListener('change', function() {
      const isChecked = this.checked;
      rowCheckboxes.forEach(checkbox => {
        checkbox.checked = isChecked;
        
        // Update Material Design checkbox state
        if (checkbox.closest('.mdc-checkbox').mdcCheckbox) {
          checkbox.closest('.mdc-checkbox').mdcCheckbox.checked = isChecked;
        }
        
        // Update row selection state
        const row = checkbox.closest('.mdc-data-table__row');
        if (row) {
          if (isChecked) {
            row.classList.add('mdc-data-table__row--selected');
          } else {
            row.classList.remove('mdc-data-table__row--selected');
          }
        }
      });
      
      // Update select all checkbox state
      if (selectAllCheckbox.closest('.mdc-checkbox').mdcCheckbox) {
        selectAllCheckbox.closest('.mdc-checkbox').mdcCheckbox.checked = isChecked;
      }
    });
  }
  
  // Handle individual row checkbox changes
  rowCheckboxes.forEach(checkbox => {
    checkbox.addEventListener('change', function() {
      const row = this.closest('.mdc-data-table__row');
      if (row) {
        if (this.checked) {
          row.classList.add('mdc-data-table__row--selected');
        } else {
          row.classList.remove('mdc-data-table__row--selected');
        }
      }
      
      // Update select all checkbox state
      if (selectAllCheckbox) {
        const allChecked = Array.from(rowCheckboxes).every(cb => cb.checked);
        const someChecked = Array.from(rowCheckboxes).some(cb => cb.checked);
        
        selectAllCheckbox.checked = allChecked;
        selectAllCheckbox.indeterminate = someChecked && !allChecked;
        
        // Update Material Design checkbox state
        if (selectAllCheckbox.closest('.mdc-checkbox').mdcCheckbox) {
          const mdcCheckbox = selectAllCheckbox.closest('.mdc-checkbox').mdcCheckbox;
          mdcCheckbox.checked = allChecked;
          mdcCheckbox.indeterminate = someChecked && !allChecked;
        }
      }
    });
  });
}

function initializeRowClicks() {
  const rows = document.querySelectorAll('.student-row');
  
  rows.forEach(row => {
    row.addEventListener('click', (e) => {
      // Don't toggle if clicking action buttons or menus
      if (e.target.closest('.action-button') || 
          e.target.closest('.mdc-menu') || 
          e.target.closest('.mdc-data-table__row-actions') ||
          e.target.closest('.mdc-checkbox')) {
        return;
      }
      
      const checkbox = row.querySelector('.row-checkbox');
      if (checkbox) {
        checkbox.checked = !checkbox.checked;
        checkbox.dispatchEvent(new Event('change'));
      }
    });
  });
}

function initializeFilters() {
  // Initialize filter functionality if needed
  // This can be expanded based on your filter requirements
}

function initializeActionMenus() {
  // Initialize action menus for each row
  document.querySelectorAll('[data-mdc-menu-button]').forEach(button => {
    const menuElement = button.parentElement.querySelector('.mdc-menu');
    if (menuElement && !menuElement.mdcMenu) {
      const menu = new mdc.menu.MDCMenu(menuElement);
      menuElement.mdcMenu = menu;
      
      button.addEventListener('click', () => {
        menu.open = !menu.open;
      });
    }
  });
}

// Search functionality
function initializeSearch() {
  const searchInput = document.getElementById('data-table-search');
  if (searchInput) {
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
      clearTimeout(searchTimeout);
      const searchTerm = this.value;
      
      searchTimeout = setTimeout(() => {
        // Trigger HTMX search request
        htmx.ajax('GET', window.location.pathname, {
          values: { search: searchTerm },
          target: '#app-content'
        });
      }, 300); // Debounce search requests
    });
  }
}

// Initialize search after components are loaded
document.addEventListener('DOMContentLoaded', function() {
  initializeSearch();
});

// Re-initialize search after HTMX requests
document.body.addEventListener('htmx:afterSwap', function() {
  initializeSearch();
});

// Custom styles for Material Design table
const customStyles = `
<style>
.student-photo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.student-photo-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.student-name-container {
  display: flex;
  flex-direction: column;
}

.student-name-primary {
  font-weight: 500;
  color: var(--md-on-surface);
}

.student-name-secondary {
  font-size: 0.875rem;
  color: var(--md-on-surface-variant);
  margin-top: 2px;
}

.class-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  background-color: var(--md-primary-container);
  color: var(--md-on-primary-container);
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.class-unassigned {
  color: var(--md-on-surface-variant);
}

.gender-badge {
  font-weight: 500;
}

.status-chip.status-active {
  --mdc-chip-label-text-color: var(--md-on-primary-container);
  --mdc-chip-container-color: var(--md-primary-container);
}

.status-chip.status-inactive {
  --mdc-chip-label-text-color: var(--md-on-error-container);
  --mdc-chip-container-color: var(--md-error-container);
}

.amount-cell {
  font-weight: 500;
  font-variant-numeric: tabular-nums;
}

.amount-positive {
  color: var(--md-tertiary);
}

.amount-negative {
  color: var(--md-error);
}

.amount-warning {
  color: #f57c00;
}

.sticky-column {
  position: sticky;
  left: 0;
  background-color: var(--md-surface);
  z-index: 1;
}

.show-on-pc {
  display: none;
}

.show-on-phone {
  display: inline-flex;
}

@media (min-width: 768px) {
  .show-on-pc {
    display: inline-flex;
  }
  
  .show-on-phone {
    display: none;
  }
}

.mdc-data-table__row-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.mdc-data-table__row-actions .mdc-icon-button {
  width: 32px;
  height: 32px;
}

.mdc-data-table__row-actions .mdc-icon-button .material-icons {
  font-size: 18px;
}
</style>
`;

// Inject custom styles
if (!document.getElementById('material-students-table-styles')) {
  const styleElement = document.createElement('div');
  styleElement.id = 'material-students-table-styles';
  styleElement.innerHTML = customStyles;
  document.head.appendChild(styleElement);
}
</script>
