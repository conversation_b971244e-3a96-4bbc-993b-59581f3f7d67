<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Material Design Table</title>

    <!-- Material Icons and Roboto Font -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Components Web CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">

    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Material Design Data Table Enhancements */
        .data-table-card {
            margin: 24px 0;
        }

        .data-table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            border-bottom: 1px solid #e0e0e0;
        }

        .filter-toolbar {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .data-table-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .search-field {
            min-width: 200px;
        }

        /* Student Photo Styling */
        .student-photo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .student-photo-container {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Student Name Styling */
        .student-name-container {
            display: flex;
            flex-direction: column;
        }

        .student-name-primary {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
        }

        .student-name-secondary {
            font-size: 0.875rem;
            color: rgba(0, 0, 0, 0.6);
            margin-top: 2px;
        }

        /* Class Badge Styling */
        .class-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            background-color: #e3f2fd;
            color: #1976d2;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .class-unassigned {
            color: rgba(0, 0, 0, 0.6);
        }

        .class-unassigned .material-icons {
            font-size: 18px;
        }

        /* Gender Badge */
        .gender-badge {
            font-weight: 500;
        }

        /* Status Chip Styling */
        .status-chip.status-active {
            --mdc-chip-label-text-color: #1976d2;
            --mdc-chip-container-color: #e3f2fd;
        }

        .status-chip.status-inactive {
            --mdc-chip-label-text-color: #d32f2f;
            --mdc-chip-container-color: #ffebee;
        }

        /* Amount Cell Styling */
        .amount-cell {
            font-weight: 500;
            font-variant-numeric: tabular-nums;
        }

        .amount-positive {
            color: #388e3c;
        }

        .amount-negative {
            color: #d32f2f;
        }

        .amount-warning {
            color: #f57c00;
        }

        /* Row Actions */
        .mdc-data-table__row-actions {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .mdc-data-table__row-actions .mdc-icon-button {
            width: 32px;
            height: 32px;
        }

        .mdc-data-table__row-actions .mdc-icon-button .material-icons {
            font-size: 18px;
        }

        /* Student ID Styling */
        .student-id {
            font-family: 'Roboto Mono', monospace;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Material Design Students Table</h1>
        
        <!-- Material Design Data Table -->
        <div class="mdc-card data-table-card mdc-card--outlined">
            <!-- Data Table Header with Actions -->
            <div class="data-table-header">
                <!-- Filter Toolbar -->
                <div class="filter-toolbar">
                    <button class="mdc-icon-button" aria-label="Filter">
                        <div class="mdc-icon-button__ripple"></div>
                        <span class="material-icons">filter_list</span>
                    </button>
                </div>

                <!-- Data Table Actions -->
                <div class="data-table-actions">
                    <!-- Search Field -->
                    <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon search-field">
                        <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">search</span>
                        <input id="data-table-search" class="mdc-text-field__input" type="text" placeholder="Rechercher...">
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label class="mdc-floating-label">Rechercher</label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>

                    <!-- More Options Button -->
                    <button class="mdc-icon-button" aria-label="More options">
                        <div class="mdc-icon-button__ripple"></div>
                        <span class="material-icons">more_vert</span>
                    </button>
                </div>
            </div>

            <!-- Data Table -->
            <div class="mdc-data-table">
                <div class="mdc-data-table__table-container">
                    <table class="mdc-data-table__table">
                        <thead>
                            <tr class="mdc-data-table__header-row">
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox">
                                    <div class="mdc-checkbox mdc-data-table__header-row-checkbox">
                                        <input type="checkbox" class="mdc-checkbox__native-control" id="select-all" aria-label="Toggle all rows"/>
                                        <div class="mdc-checkbox__background">
                                            <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                            </svg>
                                            <div class="mdc-checkbox__mixedmark"></div>
                                        </div>
                                    </div>
                                </th>
                                <th class="mdc-data-table__header-cell" style="width: 60px">Photo</th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort" role="columnheader" scope="col" aria-sort="none">
                                    <div class="mdc-data-table__header-cell-wrapper">
                                        <div class="mdc-data-table__header-cell-label">Nom et Prénoms</div>
                                        <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Sort by Name">arrow_upward</button>
                                    </div>
                                </th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort" role="columnheader" scope="col" aria-sort="none">
                                    <div class="mdc-data-table__header-cell-wrapper">
                                        <div class="mdc-data-table__header-cell-label">Matricule</div>
                                        <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Sort by ID">arrow_upward</button>
                                    </div>
                                </th>
                                <th class="mdc-data-table__header-cell">Classe</th>
                                <th class="mdc-data-table__header-cell">Sexe</th>
                                <th class="mdc-data-table__header-cell">Statut</th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--numeric">Arriéré</th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--numeric">À payer</th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--numeric">Payé</th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--numeric">Reste</th>
                                <th class="mdc-data-table__header-cell">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="mdc-data-table__content">
                            <!-- Sample Row 1 -->
                            <tr class="mdc-data-table__row">
                                <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                    <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                        <input type="checkbox" class="mdc-checkbox__native-control" aria-labelledby="u0"/>
                                        <div class="mdc-checkbox__background">
                                            <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                            </svg>
                                            <div class="mdc-checkbox__mixedmark"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="mdc-data-table__cell">
                                    <div class="student-photo-container">
                                        <div class="student-photo" style="background-color: #e3f2fd; display: flex; align-items: center; justify-content: center;">
                                            <span class="material-icons" style="color: #1976d2;">person</span>
                                        </div>
                                    </div>
                                </td>
                                <td class="mdc-data-table__cell">
                                    <div class="student-name-container">
                                        <div class="student-name-primary">Jean Dupont</div>
                                        <div class="student-name-secondary">جان دوبون</div>
                                    </div>
                                </td>
                                <td class="mdc-data-table__cell">
                                    <span class="student-id">STU001</span>
                                </td>
                                <td class="mdc-data-table__cell">
                                    <span class="class-badge">6ème A</span>
                                </td>
                                <td class="mdc-data-table__cell">
                                    <span class="gender-badge">M</span>
                                </td>
                                <td class="mdc-data-table__cell">
                                    <div class="mdc-chip status-chip status-active">
                                        <div class="mdc-chip__ripple"></div>
                                        <span class="mdc-chip__text">Actif</span>
                                    </div>
                                </td>
                                <td class="mdc-data-table__cell mdc-data-table__cell--numeric">
                                    <span class="amount-cell amount-negative">5,000</span>
                                </td>
                                <td class="mdc-data-table__cell mdc-data-table__cell--numeric">
                                    <span class="amount-cell">50,000</span>
                                </td>
                                <td class="mdc-data-table__cell mdc-data-table__cell--numeric">
                                    <span class="amount-cell amount-positive">45,000</span>
                                </td>
                                <td class="mdc-data-table__cell mdc-data-table__cell--numeric">
                                    <span class="amount-cell amount-warning">5,000</span>
                                </td>
                                <td class="mdc-data-table__cell">
                                    <div class="mdc-data-table__row-actions">
                                        <button class="mdc-icon-button" aria-label="Edit">
                                            <div class="mdc-icon-button__ripple"></div>
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="mdc-icon-button" aria-label="Payment">
                                            <div class="mdc-icon-button__ripple"></div>
                                            <span class="material-icons">attach_money</span>
                                        </button>
                                        <button class="mdc-icon-button" aria-label="More">
                                            <div class="mdc-icon-button__ripple"></div>
                                            <span class="material-icons">more_vert</span>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Material Components Web JavaScript -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>

    <script>
        // Initialize Material Design components
        document.addEventListener('DOMContentLoaded', function() {
            mdc.autoInit();
        });
    </script>
</body>
</html>
