from typing import Any
from io import BytesIO
from zipfile import ZipFile
from threading import Thread, Lock
import requests, ssl, datetime
from django.contrib import admin
from django.contrib.admin import ModelAdmin
from django.db.models.aggregates import Count
from django.utils import timezone
from django.http import HttpResponse
from django.db.models import Q, Subquery, OuterRef, F
from django.utils.html import format_html
from django import forms
from django.shortcuts import render
from django.utils.translation import gettext_lazy as _
from django.urls import reverse
from import_export.admin import (ExportActionModelAdmin, ImportExportActionModelAdmin)
from django.contrib.admin import TabularInline
from .resources import StudentResource, StaffResource
from . import models
from exams.views import get_session_year
from exams import grades_utils
from exams.models import LevelSubject
from .tasks import initialize_school_for_active_year
from main import utils as main_utils

ModelAdmin.list_per_page = 25

class YearSelectionForm(forms.Form):
    source_year = forms.ModelChoiceField(
        queryset=models.Year.objects.all().order_by('-name'),
        label=_("Année source"),
        required=True
    )
    target_year = forms.ModelChoiceField(
        queryset=models.Year.objects.all().order_by('-name'),
        label=_("Année cible"), 
        required=True
    )

@admin.register(models.Year)
class YearAdmin(ModelAdmin):
    list_display = ['full_name', 'name']
    search_fields = ['full_name', 'name']

@admin.register(models.Location)
class LocationAdmin(ImportExportActionModelAdmin):
    list_display = ['code', 'name']


@admin.register(models.School)
class SchoolAdmin(ModelAdmin):
    list_display = [
        'id', 'code', 'name', 'location', 
        'founder', 'phone1', 
        'status', 'cycle',
        'effectif_actuel',
        'association', 
        'created_at',
    ]
    list_filter = ['location', 'cycle', 'status']
    actions = [
        'create_subjects', 
        'create_subjects_for_previous_year',
        'add_school_plan_subscription', 
        'add_subscription',
        'copy_subjects_to_year',
        'generate_initial_subschool'
    ]
    search_fields = ['code', 'name']
        
    @admin.action(description='Actualisation Nouvelle année')
    def add_subscription(self, request, queryset):
        user = request.user
        for school in queryset:
            initialize_school_for_active_year.delay(school.id, user.id)

    @admin.action(description='Créer les matières par défaut')
    def create_subjects(self, request, queryset):
        year = models.Year.objects.get(active=True)
        for obj in queryset:
            grades_utils.create_default_subjects_and_terms(school=obj, year=year)
    
    @admin.action(description='Créer les matières par défaut - année précédente')
    def create_subjects_for_previous_year(self, request, queryset):
        year = models.Year.objects.get(active=True)
        for obj in queryset:
            grades_utils.create_default_subjects_and_terms(school=obj, year=year.previous)

    @admin.action(description='Souscrire abonnement pour Ecole')
    def add_school_plan_subscription(self, request, queryset):
        year = models.Year.objects.get(active=True)
        for school in queryset:
            subscription = models.Subscription.objects.filter(school=school, year=year).first()
            if not subscription:
                models.Subscription.objects.create(
                    school=school, year=year, 
                    plan=models.Subscription.PLAN_SCHOOL)
            elif subscription.plan != models.Subscription.PLAN_SCHOOL:
                subscription.plan = models.Subscription.PLAN_SCHOOL
                subscription.save(update_fields=['plan'])

    @admin.action(description=_("Copier les matières vers une autre année"))
    def copy_subjects_to_year(self, request, queryset):
        if 'apply' in request.POST:
            form = YearSelectionForm(request.POST)
            if form.is_valid():
                source_year = form.cleaned_data['source_year']
                target_year = form.cleaned_data['target_year']
                
                copied = 0
                for school in queryset:
                    # Get all source subjects for this school
                    source_subjects = LevelSubject.objects.filter(
                        school=school,
                        year=source_year
                    )
                    
                    # Copy each subject to target year
                    for subject in source_subjects:
                        LevelSubject.objects.update_or_create(
                            school=school,
                            year=target_year,
                            subject=subject.subject,
                            level=subject.level,
                            defaults={
                                'coefficient': subject.coefficient,
                                'active': subject.active,
                                'order': subject.order,
                                'max': subject.max
                            }
                        )
                        copied += 1

                self.message_user(
                    request,
                    _(f"{copied} matières copiées avec succès vers l'année {target_year}")
                )
                return None

        form = YearSelectionForm()
        
        return render(
            request,
            'admin/year_selection_form.html',
            context={
                'title': _("Sélectionner l'année cible"),
                'queryset': queryset,
                'form': form,
                'action': 'copy_subjects_to_year',
            }
        )
    
    def get_queryset(self, request):
        year = models.Year.objects.get(active=True)
        return super().get_queryset(request).annotate(
            eleves=Count('enrollment', filter=Q(enrollment__year=year, enrollment__active=True))
        )
    
    def effectif_actuel(self, school):
        return school.eleves
    
    def generate_initial_subschool(self, request, queryset):
        for school in queryset:
            if school.subschool_set.count() == 0:
                models.Subschool.objects.create(
                    name=school.name, name_ar=school.translation, 
                    school=school, is_main_school=True)


@admin.register(models.Subschool)
class SubschoolAdmin(ModelAdmin):
    list_display = ['name', 'name_ar', 'school']
    list_filter = ['school']
    search_fields = ['code', 'name']
    list_select_related = ['school']


@admin.register(models.GenericLevel)
class GenericLevelAdmin(ImportExportActionModelAdmin):
    list_display = ['order', 'name', 'short_name', 'cycle']
    list_filter = ['cycle']


@admin.register(models.Founder)
class FounderAdmin(ModelAdmin):
    list_display = ['name', 'phone', 'schools']

    def get_queryset(self, request):
        return super().get_queryset(request)\
        .annotate(schools=Count('school'))
    
    @admin.display(description="nbre d'écoles")
    def schools(self, founder):
        return founder.schools


@admin.register(models.Student)
class StudentAdmin(ModelAdmin):
    model = models.Student
    list_display = [
        'identifier', 'student_id', 'last_name',
        'nationality', 'birth_day', 'birth_month', 'birth_year', 
        'first_name', 'gender', 'origin', 'school',
        'enrollments', 'last_enrolled'
    ]
    list_select_related = ['origin', 'school']
    list_filter = ['school', 'origin', 'gender', 'nationality']
    autocomplete_fields = ['school', 'origin']
    actions = ['delete_students_with_no_enrollments']
    search_fields = ['student_id', 'last_name', 'first_name', 'identifier']

    def enrollments(self, student):
        return student.enrollments
    
    def last_enrolled(self, student):
        return student.last_enrolled

    def get_queryset(self, request):
        return super().get_queryset(request) \
            .annotate(
                enrollments=Count('enrollment', distinct=True), 
                last_enrolled=Subquery(
                    models.Enrollment.objects.filter(
                    student=OuterRef('pk')
                    ).select_related('year').only('year__name') \
                    .order_by('-year__name').values('year__name')[:1]
            )
        )

    @admin.action(description='Effacer les élèves non inscrits')
    def delete_students_with_no_enrollments(self, request, queryset):
        qs  = queryset.annotate(
                enrollments=Count('enrollment', distinct=True), 
                last_enrolled=Subquery(
                    models.Enrollment.objects.filter(
                    student=OuterRef('pk')
                    ).select_related('year').only('year__name') \
                    .order_by('-year__name').values('year__name')[:1]
        )).filter(enrollments=0)

        qs.delete()
@admin.action(description='Annuler les classes')
def cancel_level_attribution(modeladmin, request, queryset):
    queryset.update(level_fr=None, level_ar=None)


def download_and_add_image(url, file, lock, new_name=None):
    if url:
        # Extract original extension
        extension = url.split(".")[-1] if "." in url else "jpg"
        
        # Use provided name or original filename
        filename = f"{new_name}.{extension}" if new_name else url.split("/")[-1]
        
        resp = requests.get(url)
        if resp.status_code == 200:
            with lock:  # Acquire lock before writing to ZIP file
                file.writestr(filename, resp.content)

@admin.display(description='Générer les photos en fichier ZIP compressé')
def generate_zip_action(modeladmin, request, queryset):
    # Create a BytesIO object to hold the ZIP file in memory
    zip_buffer = BytesIO()

    # Create a lock for thread safety
    lock = Lock()

    threads = []
    with ZipFile(zip_buffer, 'w', compresslevel=0) as zip_file:  # Disable compression for faster generation
        for enrollment in queryset:
            # Define the logic to generate content for each enrollmentect in the ZIP
            # (e.g., download images, create text files, etc.)
            # Replace this with your specific logic
            photo = enrollment.student.photo
            url = photo.url if photo else '-'  # Replace with your URL generation logic
            thread = Thread(target=download_and_add_image, args=(url, zip_file, lock, enrollment.student.student_id or enrollment.student.identifier))
            thread.start()
            threads.append(thread)

        for thread in threads:
            thread.join()

    # Set the content type and filename for the response
    response = HttpResponse(zip_buffer.getvalue(), content_type='application/zip')
    response['Content-Disposition'] = f'attachment; filename={str(queryset.first().school)} PHOTOS.zip'

    return 
    

@admin.display(description='Générer les photos en fichier ZIP compressé')
def generate_zip_action_with_names(modeladmin, request, queryset):
    # Create a BytesIO object to hold the ZIP file in memory
    zip_buffer = BytesIO()

    # Create a lock for thread safety
    lock = Lock()

    threads = []
    with ZipFile(zip_buffer, 'w', compresslevel=0) as zip_file:  # Disable compression for faster generation
        for enrollment in queryset:
            # Define the logic to generate content for each enrollmentect in the ZIP
            # (e.g., download images, create text files, etc.)
            # Replace this with your specific logic
            photo = enrollment.student.photo
            url = photo.url if photo else '-'  # Replace with your URL generation logic
            thread = Thread(target=download_and_add_image, args=(url, zip_file, lock, enrollment.student.student_id or enrollment.student.get_full_name()))
            thread.start()
            threads.append(thread)

        for thread in threads:
            thread.join()

    # Set the content type and filename for the response
    response = HttpResponse(zip_buffer.getvalue(), content_type='application/zip')
    response['Content-Disposition'] = f'attachment; filename={str(queryset.first().school)} PHOTOS.zip'

    return response

@admin.register(models.Enrollment)
class EnrollmentAdmin(ExportActionModelAdmin):
    resource_class = StudentResource
    model = models.Enrollment
    list_display = [
         'year', 'school', 'photo', 'last_name', 'first_name', 'gender', 'level_fr', 
        'level_ar', 'active', 
        'enrollment_fees', 'year_fees', 'qualite',
        'status', 'agent', 'created_at'
    ]
    list_select_related = [
        'year', 'school', 'student', 'level_fr', 
        'level_ar', 
        'agent'
    ]
    list_filter = [
        'year', 'school', 'generic_level_fr__cycle', 'generic_level_fr', 'generic_level_ar', 
        'student__gender', 'active', 'student__birth_year', 
        'student__nationality', 'qualite', 'status', 
    ]
    search_fields = [
        'student__last_name', 'student__first_name', 'student__identifier',
        'student__student_id'
    ]
    actions = [
        cancel_level_attribution, 
        'cancel_annexe_fees',
        'mark_as_enrolled',
        'cancel_cherifla_application',
        generate_zip_action,
        generate_zip_action_with_names,
        'fix_incorrect_generic_level'
    ]
    readonly_fields = ['agent', 'school']
    autocomplete_fields = ['school', 'student', 'year', 'level_fr', 'level_ar']

    @admin.display(ordering='student__last_name')
    def last_name(self, enrollment):
        return enrollment.student.last_name
    
    @admin.display(ordering='student__first_name')
    def first_name(self, enrollment):
        return enrollment.student.first_name
    
    def gender(self, enrollment):
        return enrollment.student.gender
    
    @admin.action(description='Annuler les frais annexes')
    def cancel_annexe_fees(self, request, queryset):
        queryset.update(annexe_fees=0)
    
    @admin.action(description='Marquer comme inscrits')
    def mark_as_enrolled(self, request, queryset):
        queryset.update(active=True)
    
    @admin.action(description='Annuler les dossiers CHERIFLA')
    def cancel_cherifla_application(self, request, queryset):
        queryset.update(cherifla=False)

    @admin.action(description='Corriger les erreurs de niveau')
    def fix_incorrect_generic_level(self, request, queryset):
        queryset = queryset.exclude(
            Q(level_fr__generic_level=F('generic_level_fr')) | Q(level_fr__isnull=True)
        )
        
        objs_to_update = []
        for enrollment in queryset:
            generic_level = enrollment.level_fr.generic_level
            enrollment.generic_level_fr = generic_level
            objs_to_update.append(enrollment)
        
        models.Enrollment.objects.bulk_update(objs_to_update, fields=['generic_level_fr'])


    # def copy_term2_grades_to_term3(self, request, queryset):
    #     queryset = queryset.annotate(
    #         eps_exists=Count('grade', filter=Q(grade__school_term__code='trim3') & Q(grade__subject__subject__code='eps')),
    #         conduite_exists=Count('grade', filter=Q(grade__school_term__code='trim3') & Q(grade__subject__subject__code='conduite')),
    #     ).prefetch_related('grade_set')


    def photo(self, enrollment):
        if enrollment.student.photo:
            return format_html(f'<a href="{enrollment.student.photo.url}">Voir la photo</a>')
        return 'Aucune photo'

    def get_queryset(self, request):
        return super().get_queryset(request) \
        .annotate(
            is_second_cycle_fr=Q(generic_level_fr__cycle=main_utils.CYCLE_SECONDARY),
        ) \
        .only(
            'school__name', 'student__last_name', 'student__first_name',
            'student__gender', 'qualite', 'level_fr__number',
            'level_ar__number', 'active', 'year__full_name',
            'enrollment_fees', 'year_fees', 'annexe_fees',
            'status', 'agent__last_name', 'created_at', 'student__photo',
        )



@admin.register(models.Level)
class LevelAdmin(ModelAdmin):
    list_display = [
        'education', 'year', 'school', 'generic_level', 'get_name',
        'students_count', 'active_students_count'
    ]
    list_filter = ['education', 'year', 'school', 'generic_level']
    search_fields = ['number', 'year']
    actions = ['delete_levels_without_enrollments']

    @admin.display(description='classe')
    def get_name(self, level):
        return level.get_name()
    
    @admin.display(description='Total élèves')
    def students_count(self, level):
        return level.total_students
    
    @admin.display(description='Élèves actifs')
    def active_students_count(self, level):
        return level.active_students
    
    @admin.action(description='Supprimer les classes sans inscriptions')
    def delete_levels_without_enrollments(self, request, queryset):
        levels_to_delete = []
        deleted_count = 0
        
        for level in queryset:
            enrollment_count = models.Enrollment.objects.filter(
                level_fr=level
            ).union(
                models.Enrollment.objects.filter(level_ar=level)
            ).count()
            
            if enrollment_count == 0:
                levels_to_delete.append(level.id)
        
        if levels_to_delete:
            deleted_count = models.Level.objects.filter(id__in=levels_to_delete).delete()[0]
            
        self.message_user(
            request,
            _(f"{deleted_count} classes sans inscriptions ont été supprimées.")
        )
    
    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            total_students=Count(
                'enrollment', 
                distinct=True
            ) + Count(
                'enrollment_ar',
                distinct=True
            ),
            active_students=Count(
                'enrollment',
                filter=Q(enrollment__active=True),
                distinct=True
            ) + Count(
                'enrollment_ar',
                filter=Q(enrollment__active=True),
                distinct=True
            )
        )


@admin.register(models.Payment)
class PaymentAdmin(ModelAdmin):
    list_display = ['matricule', 'get_enrollment', 'amount', 'payment_type', 'agent', 'created_at', 'updated_at', 'school']
    list_select_related = ['enrollment__student', 'agent', 'enrollment__school']
    autocomplete_fields = ['enrollment', 'agent']
    readonly_fields = ['agent', 'school']
    actions = ['fix_payment_issue', 'set_payment_date_on_today']
    list_filter = ['payment_type', 'created_at', 'updated_at']

    @admin.display(ordering='enrollment__student__last_name', description='élève')
    def get_enrollment(self, payment):
        return str(payment.enrollment)

    def matricule(self, payment):
        return payment.enrollment.student.student_id or payment.enrollment.student.identifier
    
    def school(self, payment):
        return payment.enrollment.school

    @admin.action(description='Corriger les problèmes de paiement')
    def fix_payment_issue(self, request, queryset):
        qs = queryset.filter(
            created_at__date=datetime.date(2024, 11, 13), 
            updated_at__date=datetime.date(2024, 11, 14)
        )
        print(qs.count(), "paiements à corriger")
        qs.update(created_at=timezone.now())
    
    @admin.action(description='Définir paiement sur la date actuelle')
    def set_payment_date_on_today(self, request, queryset):
        queryset.update(created_at=timezone.now())


@admin.register(models.Holiday)
class HolidayAdmin(ModelAdmin):
    list_display = ['year', 'name', 'start_date', 'end_date']
    list_filter = ['year']


@admin.register(models.PriceCategory)
class PricingCategoryAdmin(ModelAdmin):
    fields = ['year', 'school', 'label']


class ExtraPricingInline(TabularInline):
    model = models.LevelExtraPrice
    fields = ['category', 'price']


@admin.register(models.LevelPricing)
class SchoolPricingAdmin(ModelAdmin):
    list_display = ['year', 'school', 'generic_level', 'inscription', 'scolarite']
    list_select_related = ['generic_level', 'school', 'year']
    list_filter = ['year', 'generic_level', 'school']
    inlines = [ExtraPricingInline]


@admin.register(models.LevelExtraPrice)
class ExtraPricingAdmin(ModelAdmin):
    list_display = ['pricing', 'category', 'price']


@admin.register(models.Expense)
class ExpenseAdmin(ModelAdmin):
    list_display = ['expense_type', 'amount', 'created_at', 'year', 'school']
    list_filter = ['expense_type', 'year', 'school']


@admin.register(models.Teacher)
class TeacherAdmin(ModelAdmin):
    list_select_related = ['user__school']
    list_filter = ['user__school']
    list_display = [
        'last_name', 'first_name', 'username', 
        'password', 'get_levels', 'origin'
    ]
    search_fields = ['last_name', 'first_name', 'user__username']

    def username(self, teacher):
        return teacher.user.username if teacher.user else None
    
    def password(self, teacher):
        return teacher.user.custom_password if teacher.user else None
    

@admin.register(models.TeacherLevel2)
class TeacherLevelAdmin(ModelAdmin):
    list_display = ['teacher', 'get_year', 'get_generic_level', 'get_school']
    list_select_related = ['teacher', 'level__generic_level']
    list_filter = ['level__year', 'level__generic_level', 'level__school']

    autocomplete_fields = ['teacher', 'level']

    @admin.display(description='Matières')
    def get_subjects(self, teacher_level):
        return teacher_level.get_subjects()

    @admin.display(description='Année')
    def get_year(self, teacher_level):
        return teacher_level.level.year
    
    @admin.display(description='Ecole')
    def get_school(self, teacher_level):
        return teacher_level.level.school
    
    @admin.display(description='Niveau')
    def get_generic_level(self, teacher_level):
        return teacher_level.level.generic_level
    
    def get_queryset(self, request):
        return super().get_queryset(request) \
            .select_related('level__generic_level', 'level__year', 'level__school') \
            .only(
                'level__year__name', 'level__school__name', 
                'level__generic_level__name',
                'teacher__last_name', 'teacher__first_name'
            )


    

@admin.register(models.Subscription)
class SubscriptionAdmin(ModelAdmin):
    list_display = ['year', 'school', 'plan_type', 'plan']
    list_filter = ['year', 'school']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('school', 'year') \
            .only('plan_type', 'plan', 'year__name',
                  'school__name')

@admin.register(models.SubscriptionPayment)
class SubscriptionPaymentAdmin(ModelAdmin):
    list_display = ['subscription', 'amount']
    list_filter = ['subscription__year', 'subscription__school']
    

@admin.register(models.PDFFile)
class PDFFileAdmin(ModelAdmin):
    list_display = ['year', 'level', 'get_category_display', 'path', 'download_pdf']
    list_filter = ['level__year', 'category', 'level__school', 'term']
    list_select_related = ['level__year', 'level__school']

    def year(self, file):
        return file.level.year
    
    # Add a method that will return a file response with the pdf file using the file path (file.path)
    @admin.display(description='Télécharger le fichier')
    def download_pdf(self, file):
        file_path = file.path
        download_url = reverse('exams:file_download', kwargs={'id': file.id})
        return format_html(f'<a href="{download_url}">Télécharger</a>')
        
@admin.register(models.MessageBalance)
class SMSBalanceAdmin(ExportActionModelAdmin):
    list_display = ['school', 'used', 'balance']


@admin.register(models.Message)
class SMSAdmin(ExportActionModelAdmin):
    list_display = ['school', 'year', 'content', 'sms_count']
    autocomplete_fields = ['student', 'school', 'year']


@admin.register(models.MessageUpgrade)
class SMSUpgradeAdmin(ExportActionModelAdmin):
    list_display = ['school', 'amount_added']


@admin.register(models.Staff)
class StaffAdmin(ImportExportActionModelAdmin):
    list_display = ['last_name', 'first_name', 'phone', 'email', 'role', 'school']
    list_filter = ['school', 'role']
    search_fields = ['last_name', 'first_name', 'phone', 'email']
    list_select_related = ['school', 'role']


@admin.register(models.StaffRole)
class StaffRoleAdmin(ImportExportActionModelAdmin):
    list_display = ['school', 'name', 'code', 'choice_code']
    list_filter = ['school']
    search_fields = ['name', 'code']
    list_select_related = ['school']



@admin.register(models.StaffCard)
class StaffCardAdmin(ImportExportActionModelAdmin):
    list_display = (
        'matricule', 'last_name', 'first_name', 'gender', 
        'birth_date_', 'phone', 'status', 
        'birth_place', 'authorization_number', 
        'job', 'photo'
    )
    search_fields = ('matricule', 'last_name', 'first_name', 'phone')
    list_filter = ('status', 'gender', 'school')
    ordering = ('last_name', 'first_name')
    readonly_fields = ('created_at', 'updated_at')
    # actions = [generate_photos_zip_action]
    resource_class = StaffResource
    model = models.StaffCard

    fieldsets = (
        (None, {
            'fields': ('matricule', 'last_name', 'first_name', 'gender', 'birth_date', 'birth_place', 'phone', 'status', 'school', 'authorization_number', 'photo')
        }),
        ('Dates', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def get_readonly_fields(self, request, obj=None):
        if obj:  # editing an existing object
            return self.readonly_fields + ('matricule',)
        return self.readonly_fields
    
    def birth_date_(self, staff):
        return staff.birth_date_str()
    


@admin.register(models.SalaryPaymentOptions)
class SalaryPaymentOptionAdmin(admin.ModelAdmin):
    list_display = ['name', 'operation', 'amount', 'school', 'active']
    list_filter = ['operation', 'school', 'active']
    search_fields = ['name']



@admin.register(models.SalaryPaymentDecision)
class SalaryPaymentDecisionAdmin(admin.ModelAdmin):
    list_filter = ['year', 'month']
    list_display = ['year', 'month', 'payment_date', 'decision_number']
    search_fields = ['decision_number']


@admin.register(models.StaffSalaryForMonth)
class StaffSalaryForMonthAdmin(admin.ModelAdmin):
    list_display = ['staff', 'salary', 'month', 'payment_date', 'status', 'net_pay']


@admin.register(models.StaffSalaryForMonthOption)
class StaffSalaryForMonthOptionAdmin(admin.ModelAdmin):
    list_display = ['staff', 'amount', 'month']
    list_filter = ['salary__month']
    search_fields = ['staff__last_name', 'staff__first_name', 'option']

    def month(self, option):
        return option.salary.month

    def staff(self, option):
        return option.salary.staff