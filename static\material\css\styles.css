/* Preloader */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--md-background);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
}

.preloader.hidden {
    opacity: 0;
    visibility: hidden;
}

.spinner {
    width: 50px;
    height: 50px;
    position: relative;
}

.circular {
    animation: rotate 2s linear infinite;
    height: 100%;
    transform-origin: center center;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
}

.path {
    stroke: var(--md-primary);
    stroke-dasharray: 89, 200;
    stroke-dashoffset: 0;
    stroke-linecap: round;
    animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotate {
    100% {
        transform: rotate(360deg);
    }
}

@keyframes dash {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -35px;
    }
    100% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -124px;
    }
}

/* Main Layout */
:root {
    /* Dark mode flag */
    --is-dark-mode: 0;
    /* Material Design 3 colors */
    --md-primary: #1976d2; /*#009688*/
    --md-primary-rgb: 25, 118, 210; /* RGB values for primary color */
    --md-primary-container: #d3e4ff;
    --md-on-primary: #ffffff;
    --md-on-primary-container: #001d36;
    --md-secondary: #2196f3;
    --md-secondary-container: #d1e4ff;
    --md-on-secondary: #ffffff;
    --md-on-secondary-container: #001d36;
    --md-tertiary: #6750a4;
    --md-tertiary-container: #e9ddff;
    --md-on-tertiary: #ffffff;
    --md-on-tertiary-container: #22005d;
    --md-error: #b3261e;
    --md-error-container: #f9dedc;
    --md-on-error: #ffffff;
    --md-on-error-container: #410e0b;
    --md-background: #E5E5E5;
    --md-on-background: #424242;
    --md-surface: #ffffff;
    --md-on-surface: #424242;
    --md-surface-variant: #e7e0ec;
    --md-on-surface-variant: #616161;
    --md-outline: #79747e;
    --md-outline-variant: #e6e0e9;

    /* Legacy MDC theme variables for compatibility */
    --mdc-theme-primary: var(--md-primary);
    --mdc-theme-secondary: var(--md-secondary);
    --mdc-theme-background: var(--md-background);
    --mdc-theme-surface: var(--md-surface);
    --mdc-theme-error: var(--md-error);
    --mdc-theme-on-primary: var(--md-on-primary);
    --mdc-theme-on-secondary: var(--md-on-secondary);
    --mdc-theme-on-surface: var(--md-on-surface);
    --mdc-theme-on-error: var(--md-on-error);

    /* Layout */
    --sidebar-width: 256px;
    --top-app-bar-height: 64px;

    /* Font sizes */
    --font-size-base: 14px;
    --font-size-small: 12px;
    --font-size-medium: 16px;
    --font-size-large: 20px;
    --font-size-xlarge: 24px;

    /* Elevation - Lighter Material Design 3 */
    --md-elevation-level1: 0 1px 2px rgba(0,0,0,0.1), 0 1px 2px rgba(0,0,0,0.06);
    --md-elevation-level2: 0 1px 3px rgba(0,0,0,0.1), 0 2px 4px rgba(0,0,0,0.06);
    --md-elevation-level3: 0 2px 4px rgba(0,0,0,0.08), 0 2px 5px rgba(0,0,0,0.06);
    --md-elevation-level4: 0 3px 6px rgba(0,0,0,0.08), 0 2px 5px rgba(0,0,0,0.04);
    --md-elevation-level5: 0 4px 8px rgba(0,0,0,0.08), 0 2px 5px rgba(0,0,0,0.04);

    /* Spacing - Material Design 8dp grid */
    --md-spacing-1: 8px;
    --md-spacing-2: 16px;
    --md-spacing-3: 24px;
    --md-spacing-4: 32px;
    --md-spacing-5: 40px;
}

/* Dark Mode Theme */
.dark-mode {
    --is-dark-mode: 1;
    /* Material Design 3 Dark Theme colors */
    --md-primary: #90caf9; /* Lighter blue for dark mode */
    --md-primary-rgb: 144, 202, 249;
    --md-primary-container: #0d47a1;
    --md-on-primary: #000000;
    --md-on-primary-container: #d6e4ff;
    --md-secondary: #64b5f6;
    --md-secondary-container: #0d47a1;
    --md-on-secondary: #000000;
    --md-on-secondary-container: #d6e4ff;
    --md-tertiary: #b39ddb;
    --md-tertiary-container: #4527a0;
    --md-on-tertiary: #000000;
    --md-on-tertiary-container: #e9ddff;
    --md-error: #ef9a9a;
    --md-error-container: #b71c1c;
    --md-on-error: #000000;
    --md-on-error-container: #f9dedc;
    --md-background: #121212;
    --md-on-background: #e0e0e0;
    --md-surface: #1e1e1e;
    --md-on-surface: #e0e0e0;
    --md-surface-variant: #2d2d2d;
    --md-on-surface-variant: #bdbdbd;
    --md-outline: #9e9e9e;
    --md-outline-variant: #424242;

    /* Elevation - Dark Mode */
    --md-elevation-level1: 0 1px 2px rgba(0,0,0,0.3), 0 1px 3px rgba(0,0,0,0.15);
    --md-elevation-level2: 0 2px 4px rgba(0,0,0,0.3), 0 2px 5px rgba(0,0,0,0.15);
    --md-elevation-level3: 0 4px 8px rgba(0,0,0,0.3), 0 2px 5px rgba(0,0,0,0.15);
    --md-elevation-level4: 0 6px 10px rgba(0,0,0,0.3), 0 2px 5px rgba(0,0,0,0.15);
    --md-elevation-level5: 0 8px 12px rgba(0,0,0,0.3), 0 2px 5px rgba(0,0,0,0.15);
}

/* Material Design Data Table Enhancements */
.data-table-card {
    margin: var(--md-spacing-3) 0;
}

.data-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--md-spacing-2) var(--md-spacing-3);
    border-bottom: 1px solid var(--md-outline-variant);
}

.filter-toolbar {
    display: flex;
    align-items: center;
    gap: var(--md-spacing-2);
}

.data-table-actions {
    display: flex;
    align-items: center;
    gap: var(--md-spacing-2);
}

.search-field {
    min-width: 200px;
}

/* Student Photo Styling */
.student-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.student-photo-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Student Name Styling */
.student-name-container {
    display: flex;
    flex-direction: column;
}

.student-name-primary {
    font-weight: 500;
    color: var(--md-on-surface);
}

.student-name-secondary {
    font-size: 0.875rem;
    color: var(--md-on-surface-variant);
    margin-top: 2px;
}

/* Class Badge Styling */
.class-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    background-color: var(--md-primary-container);
    color: var(--md-on-primary-container);
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.class-unassigned {
    color: var(--md-on-surface-variant);
}

.class-unassigned .material-icons {
    font-size: 18px;
}

/* Gender Badge */
.gender-badge {
    font-weight: 500;
}

/* Status Chip Styling */
.status-chip.status-active {
    --mdc-chip-label-text-color: var(--md-on-primary-container);
    --mdc-chip-container-color: var(--md-primary-container);
}

.status-chip.status-inactive {
    --mdc-chip-label-text-color: var(--md-on-error-container);
    --mdc-chip-container-color: var(--md-error-container);
}

/* Amount Cell Styling */
.amount-cell {
    font-weight: 500;
    font-variant-numeric: tabular-nums;
}

.amount-positive {
    color: var(--md-tertiary);
}

.amount-negative {
    color: var(--md-error);
}

.amount-warning {
    color: #f57c00;
}

/* Sticky Column */
.sticky-column {
    position: sticky;
    left: 0;
    background-color: var(--md-surface);
    z-index: 1;
}

/* Responsive Actions */
.show-on-pc {
    display: none;
}

.show-on-phone {
    display: inline-flex;
}

@media (min-width: 768px) {
    .show-on-pc {
        display: inline-flex;
    }

    .show-on-phone {
        display: none;
    }
}

/* Row Actions */
.mdc-data-table__row-actions {
    display: flex;
    align-items: center;
    gap: 4px;
}

.mdc-data-table__row-actions .mdc-icon-button {
    width: 32px;
    height: 32px;
}

.mdc-data-table__row-actions .mdc-icon-button .material-icons {
    font-size: 18px;
}

/* Student ID Styling */
.student-id {
    font-family: 'Roboto Mono', monospace;
    font-weight: 500;
}

html {
    font-size: var(--font-size-base);
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.5;
    background-color: var(--md-background);
    color: var(--md-on-background);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* App Bar */
.app-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 10;
    background-color: var(--md-primary);
    color: var(--md-on-primary);
    box-shadow: var(--md-elevation-level1);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                background-color 0.3s ease,
                color 0.3s ease,
                box-shadow 0.3s ease;
}

/* HTMX Progress Bar */
.htmx-progress-bar {
    height: 4px;
    width: 100%;
    position: fixed;
    top: var(--top-app-bar-height);
    left: 0;
    z-index: 11;
    background-color: transparent;
    overflow: hidden;
    display: none;
}

.htmx-progress-bar::before {
    content: '';
    position: absolute;
    height: 100%;
    width: 100%;
    background-color: var(--md-primary);
    animation: htmx-indeterminate-progress 2s infinite ease-in-out;
    transform-origin: 0% 50%;
}

/* Page Content Overlay */
.dashboard-content {
    position: relative;
}

.dashboard-content::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.6);
    z-index: 10;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    pointer-events: none;
}

body.htmx-request .dashboard-content::after {
    opacity: 1;
    visibility: visible;
}

/* Dark mode overlay */
.dark-mode .dashboard-content::after {
    background-color: rgba(0, 0, 0, 0.5);
}

@keyframes htmx-indeterminate-progress {
    0% {
        transform: translateX(0) scaleX(0);
    }
    40% {
        transform: translateX(0) scaleX(0.4);
    }
    100% {
        transform: translateX(100%) scaleX(0.5);
    }
}

body.htmx-request .htmx-progress-bar {
    display: block;
}


@media screen and (max-width: 768px) {
    .app-bar-hidden {
        transform: translateY(-100%);

    }
    .app-bar-hidden ~ .main-content{
        margin-top: 0 !important;
        height: 100vh; /* Adjust height to use full viewport */
        transition: margin-top 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
}


/* Search Bar Styles */
.search-container {
    margin: 0 16px;
    width: 300px;
}

.search-field {
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 4px;
    width: 100%;
    height: 40px;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
}

.search-field:hover {
    background-color: rgba(255, 255, 255, 0.25);
}

.search-field.mdc-text-field--focused {
    background-color: rgba(255, 255, 255, 0.25);
}

.search-field .mdc-text-field__input {
    color: var(--md-on-primary);
    caret-color: var(--md-on-primary);
    padding: 0;
    height: 40px;
    line-height: 40px;
    vertical-align: middle;
}

.search-field .mdc-text-field__input::placeholder {
    color: rgba(255, 255, 255, 0.6);
    vertical-align: middle;
}

.search-field .mdc-text-field__icon {
    color: var(--md-on-primary);
    opacity: 0.9;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 24px;
    margin-top: 8px;
    margin-bottom: 8px;
}

/* Removed floating label styles as we're using placeholder only */

.search-field .mdc-line-ripple {
    background-color: var(--md-on-primary);
}

.search-field .mdc-line-ripple::before {
    border-bottom-color: rgba(255, 255, 255, 0.5);
}

.search-field .mdc-line-ripple::after {
    border-bottom-color: var(--md-on-primary);
}

/* Mobile Search Styles */
.mobile-search-icon {
    display: none;
}

.mobile-search-container {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--md-primary);
    z-index: 11;
    transform: translateY(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-search-container.open {
    transform: translateY(0);
}

.mobile-search-inner {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 16px;
}

.mobile-search-input {
    flex: 1;
    background: none;
    border: none;
    color: var(--md-on-primary);
    font-size: 1rem;
    padding: 8px;
    margin: 0 8px;
    outline: none;
}

.mobile-search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.mobile-search-close {
    color: var(--md-on-primary);
}

.mdc-top-app-bar__row {
    height: var(--top-app-bar-height);
}

.mdc-top-app-bar__title {
    font-size: 1.25rem;
    font-weight: 500;
    letter-spacing: 0.0125em;
}

.mdc-top-app-bar__action-item,
.mdc-top-app-bar__navigation-icon {
    position: relative;
    overflow: hidden;
    transition: background-color 0.2s ease;
    color: var(--md-on-primary);
    width: 48px;
    height: 48px;
    padding: 12px;
    border-radius: 50%;
    margin: 0 4px;
}

.mdc-top-app-bar__action-item:hover,
.mdc-top-app-bar__navigation-icon:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Main Content Layout */
.main-content {
    display: flex;
    margin-top: var(--top-app-bar-height);
    height: calc(100vh - var(--top-app-bar-height));
}

/* Sidebar */
/* Nested List Styles */
.mdc-list-item--collapsible {
    cursor: pointer;
    padding-right: 10px; /* Add extra padding on the right to accommodate the shifted icon */
}

.mdc-list-item--collapsible .mdc-list-item__meta {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: absolute;
    right: 10px; /* Move the toggle icon 10px from the right edge */
}

.mdc-list-item--collapsible.expanded .mdc-list-item__meta .material-icons {
    transform: rotate(180deg);
}

.mdc-list-group {
    display: block;
    padding-left: 16px;
    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    max-height: 1000px;
    opacity: 1;
    overflow: hidden;
}

.mdc-list-group--hidden {
    max-height: 0;
    opacity: 0;
}

.mdc-list-item--nested {
    padding-left: 16px;
}

.sidebar {
    width: var(--sidebar-width);
    border-right: 1px solid var(--md-outline-variant);
    background-color: var(--md-surface);
    height: 100%;
    position: fixed;
    top: var(--top-app-bar-height);
    left: 0;
    z-index: 5;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                background-color 0.3s ease,
                border-color 0.3s ease;
}

.sidebar.open {
    transform: translateX(0);
}

.sidebar.closed {
    transform: translateX(-100%);
}

.mdc-drawer__header {
    padding: var(--md-spacing-2);
    border-bottom: 1px solid var(--md-outline-variant);
}

.mdc-drawer__title {
    margin: 0;
    font-size: 1.1rem;
    color: var(--md-on-surface);
    font-weight: 500;
    letter-spacing: 0.0125em;
}

.mdc-drawer__subtitle {
    margin: 4px 0 0;
    color: var(--md-on-surface-variant);
    font-size: 0.85rem;
}

.mdc-list-item {
    height: 48px;
    /* padding: 0 var(--md-spacing-2); */
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--md-on-surface-variant);
    transition: background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    font-size: 0.9rem;
    /* border-radius: 28px; */
    /* margin: 4px 8px; */
}

.mdc-list-item:hover {
    background-color: var(--md-surface-variant);
    color: var(--md-on-surface);
}

.mdc-list-item--activated {
    background-color: var(--md-primary-container);
    color: var(--md-on-primary-container);
}

.mdc-list-item--activated:hover {
    background-color: var(--md-primary-container);
    opacity: 0.9;
}

.mdc-list-item__graphic {
    margin-right: var(--md-spacing-2);
    color: inherit;
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: var(--md-spacing-3);
    overflow-y: auto;
    transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1), margin-top 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: var(--md-background);
     /* Ensure content is scrollable */
    overscroll-behavior: contain; /* Prevent scroll chaining */
    margin-top: 5px; /* Initial margin to account for app bar */
}

.dashboard-content h1 {
    color: var(--md-on-background);
    font-weight: 400;
    font-size: 1.5rem;
    margin-top: 0.5rem;
    margin-bottom: 1.5rem;
    letter-spacing: 0.0125em;
}

.dashboard-content.full-width {
    margin-left: 0;
}

.page-content {
    max-width: 1200px;
    margin: 0 auto;
}

/* Stats Cards */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: var(--md-spacing-3);
    margin-bottom: var(--md-spacing-3);
}

.stats-card {
    padding: var(--md-spacing-2);
    border-radius: 16px;
    background-color: var(--md-surface);
    transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                background-color 0.3s ease,
                border-color 0.3s ease;
    cursor: pointer;
    box-shadow: var(--md-elevation-level1);
    border: 1px solid var(--md-outline-variant);
    position: relative;
    overflow: hidden;
    -webkit-tap-highlight-color: transparent;
}

/* Stats Card Ripple Effect */
.stats-card-ripple {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: rgba(0, 0, 0, 0.2); /* Darker gray color for better visibility */
    border-radius: 50%;
    transform: scale(0);
    animation: ripple-animation 0.8s cubic-bezier(0.4, 0, 0.2, 1); /* Keep the longer animation */
    pointer-events: none;
    z-index: 0;
}

/* Custom Tooltip Styles - Not applied to top app bar */
[data-tooltip]:not(.mdc-top-app-bar__action-item) {
    position: relative;
}

[data-tooltip]:not(.mdc-top-app-bar__action-item)::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%) scale(0.8);
    background-color: #505050;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, transform 0.2s ease, visibility 0s linear 0.2s;
    z-index: 9;
    box-shadow: var(--md-elevation-level2);
    pointer-events: none;
}

[data-tooltip]:not(.mdc-top-app-bar__action-item):hover::after {
    opacity: 0.9;
    visibility: visible;
    transform: translateX(-50%) scale(1);
    transition: opacity 0.2s ease, transform 0.2s ease, visibility 0s linear 0s;
}

.dark-mode [data-tooltip]:not(.mdc-top-app-bar__action-item)::after {
    background-color: #e0e0e0;
    color: #212121;
}

@keyframes ripple-animation {
    0% {
        transform: scale(0);
        opacity: 0.6; /* Higher starting opacity */
    }
    50% {
        opacity: 0.4; /* Maintain higher visibility longer */
    }
    100% {
        transform: scale(45); /* Good coverage */
        opacity: 0;
    }
}

.stats-card:hover {
    box-shadow: var(--md-elevation-level2);
    transform: translateY(-1px);
}

.stats-card:active {
    transform: translateY(0);
    box-shadow: var(--md-elevation-level1);
}

.card-content {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1; /* Ensure content is above the ripple */
}

.card-icon {
    background-color: var(--md-primary-container);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--md-spacing-2);
}

.card-icon .material-icons {
    color: var(--md-on-primary-container);
    font-size: 20px;
}

.card-data h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--md-on-surface);
    letter-spacing: 0.0125em;
}

.card-data p {
    margin: 4px 0 0;
    color: var(--md-on-surface-variant);
    font-size: 0.8rem;
    letter-spacing: 0.0178em;
}

/* Recent Transactions */
.recent-transactions {
    padding: var(--md-spacing-2);
    border-radius: 16px;
    margin-bottom: var(--md-spacing-3);
    transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: var(--md-surface);
    box-shadow: var(--md-elevation-level1);
    border: 1px solid var(--md-outline-variant);
}

.recent-transactions:hover {
    box-shadow: var(--md-elevation-level2);
}

.card-title {
    margin-top: 0;
    margin-bottom: var(--md-spacing-2);
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--md-on-surface);
    letter-spacing: 0.0125em;
    padding: 0 var(--md-spacing-1);
}

.mdc-data-table {
    width: 100%;
    border: none;
    border-radius: 8px;
    overflow: hidden;
}

.mdc-data-table__table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

.mdc-data-table__header-cell {
    color: var(--md-on-surface);
    font-weight: 500;
    font-size: 0.85rem;
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--md-outline-variant);
    letter-spacing: 0.0178em;
}

.mdc-data-table__cell {
    color: var(--md-on-surface-variant);
    font-size: 0.85rem;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(0,0,0,0.04);
}

.mdc-data-table__row {
    transition: background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.mdc-data-table__row:hover {
    background-color: var(--md-surface-variant);
}

.mdc-data-table__row:last-child .mdc-data-table__cell {
    border-bottom: none;
}

/* Make data table scrollable */
.data-table-card .mdc-data-table__table-container {
    /* max-height: 60vh; */
    /* overflow: auto;
    min-width: 200px; */
    overflow-x: auto;   /* show horizontal scrollbar */
    overflow-y: hidden; /* optional: hide vertical scrollbar */
    width: 100%;        /* fill parent */
}

/* 2) Let the table grow to fit its cells */
.data-table-card .mdc-data-table__table {
    table-layout: auto;     /* don't force equal column widths */
    width: max-content;     /* width = sum of all columns */
    min-width: 100%;        /* at least full container */
}


/* Status Indicators */
.status-completed, .status-pending, .status-cancelled {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-completed {
    color: #1b873b;
    background-color: #ddf4e4;
}

.status-pending {
    color: #b25000;
    background-color: #fff0d3;
}

.status-cancelled {
    color: #d32f2f;
    background-color: #ffebee;
}

/* Severity Indicators */
.severity-huge, .severity-medium, .severity-minor, .severity-negligible {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 500;
}

.severity-huge {
    background-color: rgba(244, 67, 54, 0.12);
    color: #f44336;
}

.severity-medium {
    background-color: rgba(255, 171, 0, 0.12);
    color: #ffab00;
}

.severity-minor {
    background-color: rgba(33, 150, 243, 0.12);
    color: #2196f3;
}

.severity-negligible {
    background-color: rgba(0, 200, 83, 0.12);
    color: #00c853;
}

.dark-mode .severity-huge {
    background-color: rgba(244, 67, 54, 0.2);
    color: #ff5252;
}

.dark-mode .severity-medium {
    background-color: rgba(255, 171, 0, 0.2);
    color: #ffc107;
}

.dark-mode .severity-minor {
    background-color: rgba(33, 150, 243, 0.2);
    color: #64b5f6;
}

.dark-mode .severity-negligible {
    background-color: rgba(0, 200, 83, 0.2);
    color: #5efc82;
}

/* User Drawer (Right Side Offcanvas) - Custom Implementation */
.user-drawer-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    visibility: hidden;
    transition: visibility 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-drawer-container.open {
    visibility: visible;
}

.user-drawer-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.32);
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-drawer-container.open .user-drawer-backdrop {
    opacity: 1;
}

.user-drawer {
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    background-color: var(--md-surface);
    border-left: 1px solid var(--md-outline-variant);
    box-shadow: var(--md-elevation-level1);
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.user-drawer-container.open .user-drawer {
    transform: translateX(0);
}

/* Add subtle animation to drawer content */
.user-drawer-content .mdc-list-item {
    opacity: 0;
    transform: translateX(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    transition-delay: calc(var(--item-index, 0) * 0.05s);
}

.user-drawer-container.open .user-drawer-content .mdc-list-item {
    opacity: 1;
    transform: translateX(0);
}

.close-drawer-button {
    position: absolute;
    top: 8px;
    right: 8px;
    color: var(--md-on-surface-variant);
    z-index: 1;
}

.user-drawer-header {
    padding: var(--md-spacing-2);
    position: relative;
    border-bottom: 1px solid var(--md-outline-variant);
}

.user-drawer-content {
    flex: 1;
    overflow-y: auto;
}

.user-profile-header {
    padding: var(--md-spacing-2) 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

/* Add animations for profile elements */
.user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: var(--md-primary-container);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--md-spacing-2);
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.4s ease, transform 0.4s ease;
    transition-delay: 0.1s;
}

.user-avatar .material-icons {
    font-size: 48px;
    color: var(--md-on-primary-container);
}

.user-drawer-title, .user-drawer-subtitle {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.4s ease, transform 0.4s ease;
}

.user-drawer-title {
    transition-delay: 0.2s;
}

.user-drawer-subtitle {
    transition-delay: 0.25s;
}

.user-drawer-container.open .user-avatar,
.user-drawer-container.open .user-drawer-title,
.user-drawer-container.open .user-drawer-subtitle {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.user-drawer-title {
    margin: 0;
    font-size: 1.1rem;
    color: var(--md-on-surface);
    font-weight: 500;
    letter-spacing: 0.0125em;
}

.user-drawer-subtitle {
    margin: 4px 0 0;
    color: var(--md-on-surface-variant);
    font-size: 0.85rem;
}

/* Notifications Menu */
.notifications-anchor {
    position: fixed;
    top: 56px;
    right: 56px;
    z-index: 15;
}

.notifications-menu {
    width: 360px;
    max-width: 90vw;
    max-height: 500px;
    overflow-y: auto;
    border-radius: 8px;
    box-shadow: var(--md-elevation-level2);
    transform-origin: top right;
}

.notifications-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--md-spacing-2);
    border-bottom: 1px solid var(--md-outline-variant);
}

.notifications-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--md-on-surface);
}

.notifications-list {
    padding: 0;
}

.notification-item {
    padding: var(--md-spacing-2);
    border-bottom: 1px solid rgba(0,0,0,0.04);
    height: auto;
    min-height: 72px;
}

.notification-item.unread {
    background-color: rgba(25, 118, 210, 0.05);
}

.notification-icon {
    margin-right: var(--md-spacing-2);
    color: var(--md-primary);
    background-color: var(--md-primary-container);
    padding: 8px;
    border-radius: 50%;
}

.notification-title {
    display: block;
    font-weight: 500;
    color: var(--md-on-surface);
    margin-bottom: 4px;
}

.notification-text {
    display: block;
    font-size: 0.85rem;
    color: var(--md-on-surface-variant);
    margin-bottom: 4px;
}

.notification-time {
    display: block;
    font-size: 0.75rem;
    color: var(--md-on-surface-variant);
    opacity: 0.8;
}

.notifications-footer {
    padding: var(--md-spacing-1);
    text-align: center;
    border-top: 1px solid var(--md-outline-variant);
}

/* Bottom App Bar for Mobile */
.bottom-app-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: var(--md-surface);
    box-shadow: var(--md-elevation-level2);
    z-index: 10;
    display: none; /* Hidden by default, shown on mobile */
    border-top: 1px solid var(--md-outline-variant);
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.bottom-app-bar-inner {
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 56px;
    padding: 0 8px;
}

.bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    height: 100%;
    background: none;
    border: none;
    cursor: pointer;
    color: var(--md-on-surface-variant);
    padding: 8px 0;
    position: relative;
    overflow: hidden;
    transition: color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-tap-highlight-color: transparent; /* Remove default mobile tap highlight */
}

/* Bottom Nav Ripple Container */
.bottom-nav-ripple-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
    pointer-events: none;
}

.bottom-nav-ripple {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: rgba(0, 0, 0, 0.2); /* Darker gray color for better visibility */
    border-radius: 50%;
    transform: scale(0);
    pointer-events: none;
    opacity: 0;
    will-change: transform, opacity; /* Optimize for animation */
}

.dark-mode .bottom-nav-ripple {
    background-color: rgba(255, 255, 255, 0.3); /* Lighter color for dark mode */
}

.bottom-nav-ripple.animate {
    animation: bottom-nav-ripple-animation 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    animation-delay: 0s; /* No delay */
}

@keyframes bottom-nav-ripple-animation {
    0% {
        transform: scale(0);
        opacity: 0.6;
    }
    50% {
        opacity: 0.4;
    }
    100% {
        transform: scale(25);
        opacity: 0;
    }
}

.bottom-nav-item .material-icons {
    font-size: 24px;
    margin-bottom: 4px;
}

.bottom-nav-label {
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0.01em;
}

.bottom-nav-item.active {
    color: var(--md-primary);
}

.bottom-nav-item.active::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 32px;
    height: 3px;
    background-color: var(--md-primary);
    border-radius: 3px 3px 0 0;
}

/* Bottom Actions Bar */
.bottom-actions-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 56px;
    background-color: var(--md-surface);
    box-shadow: var(--md-elevation-level2);
    z-index: 15;
    transform: translateY(100%);
    transition: transform 0.3s ease-out;
    display: flex;
    align-items: center;
    justify-content: center;
}
.bottom-actions-bar.show {
    transform: translateY(0);
}
.bottom-actions-bar__inner {
    display: flex;
    align-items: center;
    justify-content: space-between; /* align left and right groups */
    width: 100%;
    max-width: 600px;
    padding: 0 16px;
}

.bottom-actions-bar__left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.actions-select .mdc-select__anchor {
    height: 40px;
    padding-top: 4px;
    padding-bottom: 4px;
}

#action-go {
    height: 40px;
}

.close-actions {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--md-on-surface);
}
.close-actions .material-icons {
    font-size: 24px;
}

/* Responsive Design */
/* Responsive Search Bar */
@media (max-width: 960px) {
    .desktop-search {
        display: none;
    }

    .mobile-search-icon {
        display: block;
    }

    .mobile-search-container {
        display: block;
    }


    .sidebar {
        transform: translateX(-100%);
        box-shadow: var(--md-elevation-level2);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .sidebar.open {
        transform: translateX(0);
        box-shadow: var(--md-elevation-level3);
    }

    /* Add a backdrop for mobile sidebar */
    .sidebar-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.32);
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 4;
        visibility: hidden;
        cursor: pointer;
    }

    .sidebar.open ~ .sidebar-backdrop {
        opacity: 1;
        pointer-events: auto;
        visibility: visible;
    }

    .dashboard-content {
        margin-left: 0;
        padding: var(--md-spacing-2);
        padding-bottom: calc(56px + var(--md-spacing-2)); /* Add space for bottom app bar */
    }

    /* Show bottom app bar on mobile */
    .bottom-app-bar {
        display: block;
    }

    .stats-cards {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: var(--md-spacing-2);
    }

    .card-title {
        font-size: 1rem;
    }

}

@media (max-width: 600px) {
    .stats-cards {
        grid-template-columns: 1fr;
        gap: var(--md-spacing-2);
    }

    .mdc-data-table {
        overflow-x: auto;
    }

    .mdc-data-table__header-cell,
    .mdc-data-table__cell {
        padding: 5px 2px;
    }

    .card-icon {
        width: 40px;
        height: 40px;
    }

    .card-icon .material-icons {
        font-size: 18px;
    }
}

/* Enhanced Data Table Styles */
.data-table-card {
    padding: 0;
    overflow: hidden;
    margin-bottom: var(--md-spacing-3);
}

/* Data Table Header with Actions */
.data-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--md-spacing-2) var(--md-spacing-3);
    flex-wrap: wrap;
    gap: var(--md-spacing-2);
    border-bottom: 1px solid var(--md-outline-variant);
}

.data-table-toolbar, .filter-toolbar, .data-table-actions {
    display: flex;
    align-items: center;
    gap: var(--md-spacing-1);
}

/* Material Search Field */
.search-field {
    width: 240px;
    margin-right: var(--md-spacing-1);
    height: 40px;
    border-radius: 4px;
    overflow: visible;
}

.search-field .mdc-text-field__input {
    caret-color: var(--md-primary);
    padding: 12px 16px 12px 35px !important;
}

.search-field .mdc-text-field__icon--leading {
    color: var(--md-on-surface-variant);
    left: -15px;
    position: absolute;
    top: 30%;
    transform: translateY(-50%);
}

.search-field .mdc-notched-outline .mdc-notched-outline__leading,
.search-field .mdc-notched-outline .mdc-notched-outline__notch,
.search-field .mdc-notched-outline .mdc-notched-outline__trailing {
    border-color: var(--md-outline-variant);
    transition: border-color 0.15s ease;
}

.search-field:hover .mdc-notched-outline .mdc-notched-outline__leading,
.search-field:hover .mdc-notched-outline .mdc-notched-outline__notch,
.search-field:hover .mdc-notched-outline .mdc-notched-outline__trailing {
    border-color: var(--md-on-surface-variant);
}

.search-field.mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__leading,
.search-field.mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__notch,
.search-field.mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__trailing {
    border-color: var(--md-primary);
    border-width: 2px;
}

.search-field .mdc-floating-label {
    left: 48px;
    color: var(--md-on-surface-variant);
    font-size: 1rem;
}

.search-field.mdc-text-field--focused .mdc-floating-label {
    color: var(--md-primary);
}

/* Search Clear Button */
.search-clear-button {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    padding: 0;
    font-size: 18px;
    color: var(--md-on-surface-variant);
    z-index: 1;
    cursor: pointer;
    background: none;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    overflow: hidden;
}

.search-clear-button:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

.dark-mode .search-clear-button:hover {
    background-color: rgba(255, 255, 255, 0.08);
}

.search-clear-button .mdc-icon-button__ripple {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    border-radius: inherit;
}

.data-table-actions .search-field {
    width: 240px;
    margin-right: var(--md-spacing-1);
}

/* Filter Chips */
.mdc-chip-set {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.mdc-chip {
    height: 32px;
    border-radius: 16px;
    background-color: var(--md-surface-variant);
    display: inline-flex;
    align-items: center;
    padding: 0 12px 0 12px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: background-color 0.2s ease;
}

.mdc-chip__ripple {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    border-radius: inherit;
}

.mdc-chip__icon {
    font-size: 18px;
    margin-right: 4px;
    color: var(--md-on-surface-variant);
    border: none;
    background-color: var(--md-surface-variant);
}

.mdc-chip__text {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--md-on-surface-variant);
}

.mdc-chip__icon--trailing {
    margin-left: 4px;
    margin-right: 0;
    font-size: 16px;
    cursor: pointer;
}

/* Data Table Row Actions */
.mdc-data-table__row-actions {
    display: flex;
    gap: 3px;
}

/* Data Table Pagination */
.mdc-data-table__pagination {
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid var(--md-outline-variant);
    padding: var(--md-spacing-2) var(--md-spacing-3);
}

.mdc-data-table__pagination-trailing {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-wrap: wrap;
    gap: var(--md-spacing-3);
}

.mdc-data-table__pagination-rows-per-page {
    display: flex;
    align-items: center;
    gap: var(--md-spacing-2);
}

.mdc-data-table__pagination-rows-per-page-label {
    font-size: 0.875rem;
    color: var(--md-on-surface-variant);
}

.mdc-data-table__pagination-rows-per-page-select {
    width: 80px;
    margin: 0;
}

.mdc-select--outlined .mdc-select__anchor {
    padding-top: 6px;
}

.mdc-data-table__pagination-navigation {
    display: flex;
    align-items: center;
    gap: var(--md-spacing-1);
}

.mdc-data-table__pagination-button[disabled] {
    opacity: 0.38;
    pointer-events: none;
}

.mdc-data-table__pagination-button .mdc-button__icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
}

.mdc-data-table__pagination-total {
    margin-right: var(--md-spacing-2);
    font-size: 0.875rem;
    color: var(--md-on-surface-variant);
}

/* Sortable Headers */
.mdc-data-table__header-cell-wrapper {
    display: flex;
    align-items: center;
}

.mdc-data-table__sort-icon-button {
    padding: 0;
    width: 24px;
    height: 24px;
    font-size: 18px;
    margin-left: 4px;
    opacity: 0;
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.mdc-data-table__header-cell:hover .mdc-data-table__sort-icon-button {
    opacity: 0.6;
}

.mdc-data-table__header-cell[aria-sort="ascending"] .mdc-data-table__sort-icon-button,
.mdc-data-table__header-cell[aria-sort="descending"] .mdc-data-table__sort-icon-button {
    opacity: 1;
}

.mdc-data-table__header-cell[aria-sort="descending"] .mdc-data-table__sort-icon-button {
    transform: rotate(180deg);
}

/* Material Checkbox Styles */
.mdc-checkbox {
    display: inline-flex;
    position: relative;
    flex: 0 0 18px;
    box-sizing: content-box;
    width: 18px;
    height: 18px;
    line-height: 0;
    white-space: nowrap;
    cursor: pointer;
    vertical-align: middle;
    padding: 11px;
}

.mdc-checkbox__background {
    display: inline-flex;
    position: absolute;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 18px;
    height: 18px;
    border: 2px solid var(--md-on-surface-variant);
    border-radius: 2px;
    background-color: transparent;
    pointer-events: none;
    transition: background-color 90ms cubic-bezier(0.4, 0, 0.6, 1), border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);
}

.mdc-checkbox__native-control:checked ~ .mdc-checkbox__background,
.mdc-checkbox__native-control:indeterminate ~ .mdc-checkbox__background {
    border-color: var(--md-primary);
    background-color: var(--md-primary);
}

.mdc-checkbox__checkmark {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    transition: opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);
}

.mdc-checkbox__native-control:checked ~ .mdc-checkbox__background .mdc-checkbox__checkmark {
    opacity: 1;
    transition: opacity 180ms cubic-bezier(0, 0, 0.2, 1);
}

.mdc-checkbox__checkmark-path {
    stroke: var(--md-on-primary) !important;
    stroke-width: 3.12px;
    stroke-dashoffset: 29.7833385;
    stroke-dasharray: 29.7833385;
}

.mdc-checkbox__native-control:checked ~ .mdc-checkbox__background .mdc-checkbox__checkmark-path {
    stroke-dashoffset: 0;
    transition: stroke-dashoffset 180ms cubic-bezier(0, 0, 0.2, 1);
}

.mdc-checkbox__native-control {
    position: absolute;
    margin: 0;
    padding: 0;
    opacity: 0;
    cursor: inherit;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.mdc-checkbox__mixedmark {
    width: 100%;
    height: 2px;
    transform: scaleX(0) rotate(0deg);
    border-radius: 2px;
    background-color: var(--md-on-primary);
    opacity: 0;
    transition: opacity 90ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms cubic-bezier(0.4, 0, 0.6, 1);
}

.mdc-checkbox__native-control:indeterminate ~ .mdc-checkbox__background .mdc-checkbox__mixedmark {
    transform: scaleX(1) rotate(0deg);
    opacity: 1;
}

.mdc-data-table__header-row-checkbox .mdc-checkbox__background,
.mdc-data-table__row-checkbox .mdc-checkbox__background {
    border-color: var(--md-on-surface-variant);
}

.mdc-data-table__header-row-checkbox .mdc-checkbox__native-control:checked ~ .mdc-checkbox__background,
.mdc-data-table__row-checkbox .mdc-checkbox__native-control:checked ~ .mdc-checkbox__background {
    border-color: var(--md-primary);
    background-color: var(--md-primary);
}

.mdc-data-table__header-cell--checkbox,
.mdc-data-table__cell--checkbox {
    padding: 0 16px;
    width: 56px;
}

/* Checkbox hover and focus states */
.mdc-checkbox:hover .mdc-checkbox__background {
    border-color: var(--md-on-surface);
}

.mdc-checkbox .mdc-checkbox__native-control:focus ~ .mdc-checkbox__background {
    border-color: var(--md-primary);
    box-shadow: 0 0 0 2px rgba(var(--md-primary-rgb), 0.2);
}

/* Improve row hover effect for better UX */
.mdc-data-table__row:hover .mdc-checkbox__background {
    border-color: var(--md-on-surface);
}

/* Search Highlight */
.search-highlight {
    background-color: rgba(var(--md-primary-rgb), 0.12);
    padding: 2px 0;
    border-radius: 2px;
}

.dark-mode .search-highlight {
    background-color: rgba(var(--md-primary-rgb), 0.24);
}

/* Modal Dialog */
.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: none;
    justify-content: center;
    align-items: center;
}

.modal-container.open {
    display: flex;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease forwards;
}

.modal-dialog {
    position: relative;
    width: 100%;
    max-width: 500px;
    background-color: var(--md-surface);
    border-radius: 8px;
    box-shadow: var(--md-elevation-3);
    transform: translateY(20px);
    opacity: 0;
    animation: slideIn 0.3s ease forwards;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid var(--md-outline-variant);
}

.modal-header h2 {
    margin: 0;
}

.close-modal-button {
    color: var(--md-on-surface-variant);
}

.modal-content {
    padding: 24px;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 16px 24px;
    border-top: 1px solid var(--md-outline-variant);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-container.closing .modal-backdrop {
    animation: fadeOut 0.3s ease forwards;
}

.modal-container.closing .modal-dialog {
    animation: slideOut 0.3s ease forwards;
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideOut {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(20px);
        opacity: 0;
    }
}

/* Snackbar */
.mdc-snackbar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    box-sizing: border-box;
    padding: 8px;
    transform: translateY(100%);
    transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.mdc-snackbar--open {
    transform: translateY(0%);
}

.mdc-snackbar__surface {
    min-width: 344px;
    max-width: 672px;
    background-color: #333333;
    border-radius: 4px;
    box-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2),
                0px 6px 10px 0px rgba(0, 0, 0, 0.14),
                0px 1px 18px 0px rgba(0, 0, 0, 0.12);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    box-sizing: border-box;
}

.mdc-snackbar__label {
    color: #fff;
    font-size: 0.875rem;
    padding: 8px 16px;
    flex-grow: 1;
}

.mdc-snackbar__actions {
    display: flex;
    align-items: center;
    margin-left: 8px;
}

.mdc-snackbar__action {
    color: #bb86fc;
}

.dark-mode .mdc-snackbar__surface {
    background-color: #424242;
}

.dark-mode .mdc-snackbar__action {
    color: var(--md-primary);
}

@media (max-width: 480px) {
    .mdc-snackbar__surface {
        min-width: 100%;
        margin: 0 8px;
    }
}

/* Dialog Form Template */
.dialog-form-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: none;
    justify-content: center;
    align-items: center;
}

.dialog-form-container.open {
    display: flex;
}

.dialog-form-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease forwards;
}

.dialog-form {
    position: relative;
    width: 75%;
    max-width: 1200px;
    background-color: var(--md-surface);
    border-radius: 8px;
    box-shadow: var(--md-elevation-level3);
    transform: translateY(20px);
    opacity: 0;
    animation: slideIn 0.3s ease forwards;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.dialog-form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    border-bottom: 1px solid var(--md-outline-variant);
}

.dialog-form-header h2 {
    margin: 0;
    color: var(--md-on-surface);
}

.close-dialog-form-button {
    color: var(--md-on-surface-variant);
}

.dialog-form-content {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
}

.dialog-form-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 12px 24px;
    border-top: 1px solid var(--md-outline-variant);
}

.dialog-form-container.closing .dialog-form-backdrop {
    animation: fadeOut 0.3s ease forwards;
}

.dialog-form-container.closing .dialog-form {
    animation: slideOut 0.3s ease forwards;
}

/* Responsive Dialog Form */
@media (max-width: 960px) {
    .dialog-form {
        width: 85%;
    }
}

@media (max-width: 600px) {
    .dialog-form {
        width: 100%;
        height: 100%;
        max-height: 100%;
        border-radius: 0;
        margin: 0;
    }

    .dialog-form-content {
        padding: 16px;
    }
}

/* Responsive Modal */
@media (max-width: 600px) {
    .modal-dialog {
        max-width: 100%;
        height: 100%;
        max-height: 100%;
        border-radius: 0;
        margin: 0;
    }
}

/* Responsive Data Table */
@media (max-width: 960px) {
    .data-table-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .data-table-actions {
        width: 100%;
        margin-top: var(--md-spacing-1);
    }

    .data-table-actions .search-field {
        flex: 1;
    }

    .mdc-data-table__pagination-trailing {
        justify-content: space-between;
        width: 100%;
    }
}

/* Autofilled inputs */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    transition: background-color 5000s ease-in-out 0s;
}

.mdc-fw-bold {
    font-weight: bold !important;
}