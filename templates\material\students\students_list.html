{% load widget_tweaks %}
{% load humanize %}

<div method="post" action="" class="dashboard-wrapper" 
     hx-include="this" 
     hx-get="{{ request.path }}?page={{ page }}{% if education %}&education={{ education}}{% endif %}" 
     hx-trigger="saved from:body" 
     hx-target="#app-content">
  
  <!-- Filters Section -->
  {% include 'partials/active_students/filter_section.html' %}

  <!-- Material Design Data Table -->
  {% with checkbox_enabled=True pagination_enabled=True search_enabled=True %}
  {% with columns=table_columns %}
  {% include 'material/components/data-table.html' %}
    {% block table_body %}
      {% for enrollment in enrollments %}
      <tr class="mdc-data-table__row student-row {% if enrollment.selected %}mdc-data-table__row--selected{% endif %}" 
          data-row-id="{{ enrollment.id }}">
        
        <!-- Checkbox Column -->
        <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
          <div class="mdc-checkbox mdc-data-table__row-checkbox">
            <input type="checkbox" class="mdc-checkbox__native-control row-checkbox" 
                   name="check-{{ enrollment.id }}" 
                   id="check-{{ enrollment.id }}"
                   {% if enrollment.selected %}checked="checked"{% endif %}
                   aria-labelledby="u{{ forloop.counter0 }}"/>
            <div class="mdc-checkbox__background">
              <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
              </svg>
              <div class="mdc-checkbox__mixedmark"></div>
            </div>
          </div>
        </td>

        <!-- Photo Column -->
        <td class="mdc-data-table__cell">
          <div class="student-photo-container">
            {% if enrollment.student.photo %}
              <img data-original="{{ enrollment.student.photo.url }}" 
                   alt="Photo" 
                   class="student-photo lazy mdc-elevation--z1">
            {% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}
              <img data-original="{{ enrollment.student.government_photo }}" 
                   alt="Photo" 
                   class="student-photo lazy mdc-elevation--z1"
                   id="{{ enrollment.id }}"
                   onload="if (this.src.endsWith('CC')) {
                     this.src = '{{ enrollment.student.blank_photo }}'
                   }">
            {% else %}
              <img data-original="{{ enrollment.student.blank_photo }}" 
                   alt="Photo" 
                   class="student-photo lazy mdc-elevation--z1">
            {% endif %}
          </div>
        </td>

        <!-- Name Column (Sticky) -->
        <td class="mdc-data-table__cell sticky-column">
          <div class="student-name-container">
            <div class="student-name-primary">{{ enrollment.student.get_full_name }}</div>
            {% if is_arabic_school and enrollment.student.full_name_ar %}
            <div class="student-name-secondary">{{ enrollment.student.full_name_ar }}</div>
            {% endif %}
          </div>
        </td>

        <!-- Student ID Column -->
        <td class="mdc-data-table__cell">
          <span class="student-id">{{ enrollment.student.student_id|default:"-" }}</span>
        </td>

        <!-- Class (French) Column -->
        <td class="mdc-data-table__cell">
          {% if enrollment.level_fr or enrollment.generic_level_fr %}
            <span class="class-badge">{{ enrollment.level_fr|default:enrollment.generic_level_fr }}</span>
          {% elif not enrollment.level_fr %}
            <span class="class-unassigned" title="Classe non attribuée">
              <span class="material-icons">help_outline</span>
            </span>
          {% else %}
            -
          {% endif %}
        </td>

        <!-- Class (Arabic) Column -->
        {% if is_arabic_school %}
        <td class="mdc-data-table__cell">
          {% if enrollment.level_ar or enrollment.generic_level_ar %}
            <span class="class-badge">{{ enrollment.level_ar|default:enrollment.generic_level_ar }}</span>
          {% elif not enrollment.level_ar %}
            <span class="class-unassigned" title="Classe arabe non attribuée">
              <span class="material-icons">help_outline</span>
            </span>
          {% else %}
            -
          {% endif %}
        </td>
        {% endif %}

        <!-- Gender Column -->
        <td class="mdc-data-table__cell">
          <span class="gender-badge">{{ enrollment.student.gender }}</span>
        </td>

        <!-- Status Column -->
        <td class="mdc-data-table__cell">
          {% if enrollment.status %}
          <div class="mdc-chip status-chip {% if enrollment.active %}status-active{% else %}status-inactive{% endif %}">
            <div class="mdc-chip__ripple"></div>
            <span class="mdc-chip__text">{{ enrollment.status }}</span>
          </div>
          {% else %}
          -
          {% endif %}
        </td>

        <!-- Debt Column -->
        <td class="mdc-data-table__cell mdc-data-table__cell--numeric">
          <span class="amount-cell amount-negative">
            {{ enrollment.debt|intcomma|default:"0" }}
          </span>
        </td>

        <!-- Amount to Pay Column -->
        <td class="mdc-data-table__cell mdc-data-table__cell--numeric">
          <span class="amount-cell">
            {{ enrollment.amount|intcomma }}
          </span>
        </td>

        <!-- Paid Column -->
        <td class="mdc-data-table__cell mdc-data-table__cell--numeric">
          <span class="amount-cell {% if enrollment.paid %}amount-positive{% endif %}">
            {% if enrollment.paid %}{{ enrollment.paid|intcomma|default:"0" }}{% else %}-{% endif %}
          </span>
        </td>

        <!-- Remaining Column -->
        <td class="mdc-data-table__cell mdc-data-table__cell--numeric">
          <span class="amount-cell {% if enrollment.remaining == 0 %}amount-positive{% elif enrollment.remaining > 0 %}amount-warning{% endif %}">
            {% if enrollment.remaining == 0 and enrollment.get_fees_total > 0 %}
              Soldé
            {% else %}
              {{ enrollment.remaining|intcomma|default:"-" }}
            {% endif %}
          </span>
        </td>

        <!-- Actions Column -->
        <td class="mdc-data-table__cell">
          <div class="mdc-data-table__row-actions">
            {% if enrollment.active %}
              <!-- Edit Button -->
              <button class="mdc-icon-button action-button show-on-pc" 
                      aria-label="Modifier infos"
                      hx-get="{% url 'school:student_edit' enrollment.id %}?page={{ page }}"
                      hx-target="#dialog-xl">
                <div class="mdc-icon-button__ripple"></div>
                <span class="material-icons">edit</span>
              </button>

              <!-- Edit Button Mobile -->
              <button class="mdc-icon-button action-button show-on-phone" 
                      aria-label="Modifier infos"
                      hx-get="{% url 'school:student_edit_wizard' enrollment.id %}?page={{ page }}"
                      hx-target="#dialog">
                <div class="mdc-icon-button__ripple"></div>
                <span class="material-icons">edit</span>
              </button>

              <!-- Payment Button -->
              <button class="mdc-icon-button action-button" 
                      aria-label="Ajouter/Modifier un paiement"
                      hx-get="{% url 'school:payment_add' %}?student_id={{ enrollment.student.identifier }}"
                      hx-target="#dialog-xl">
                <div class="mdc-icon-button__ripple"></div>
                <span class="material-icons">attach_money</span>
              </button>

              <!-- More Actions Menu -->
              <div class="mdc-menu-surface--anchor">
                <button class="mdc-icon-button action-button" 
                        aria-label="Plus d'actions"
                        data-mdc-menu-button>
                  <div class="mdc-icon-button__ripple"></div>
                  <span class="material-icons">more_vert</span>
                </button>
                
                <div class="mdc-menu mdc-menu-surface">
                  <ul class="mdc-list" role="menu" aria-hidden="true">
                    <li class="mdc-list-item" role="menuitem">
                      <span class="mdc-list-item__ripple"></span>
                      <a href="{% url 'school:student_payments_pdf' enrollment.id %}?template=2&copies=1" 
                         target="_blank" class="mdc-list-item__text">
                        Reçu Modèle 1
                      </a>
                    </li>
                    <li class="mdc-list-item" role="menuitem">
                      <span class="mdc-list-item__ripple"></span>
                      <a href="{% url 'school:student_payments_pdf' enrollment.id %}?template=1&copies=1" 
                         target="_blank" class="mdc-list-item__text">
                        Reçu Modèle 2
                      </a>
                    </li>
                    <li class="mdc-list-divider" role="separator"></li>
                    <li class="mdc-list-item" role="menuitem">
                      <span class="mdc-list-item__ripple"></span>
                      <span class="mdc-list-item__text"
                            hx-get="{% url 'school:student_delete' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                            hx-target="#dialog">
                        Supprimer élève
                      </span>
                    </li>
                  </ul>
                </div>
              </div>

              <!-- Delete Button (Desktop) -->
              <button class="mdc-icon-button action-button show-on-pc" 
                      aria-label="Supprimer élève"
                      hx-get="{% url 'school:student_delete' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                      hx-target="#dialog">
                <div class="mdc-icon-button__ripple"></div>
                <span class="material-icons">delete</span>
              </button>

            {% else %}
              <!-- Enroll Button -->
              <button class="mdc-button mdc-button--raised action-button show-on-pc" 
                      hx-get="{% url 'school:student_edit' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}" 
                      hx-target="#dialog-xl">
                <div class="mdc-button__ripple"></div>
                <span class="material-icons mdc-button__icon">person_add</span>
                <span class="mdc-button__label">Inscrire</span>
              </button>

              <button class="mdc-button mdc-button--raised action-button show-on-phone" 
                      hx-get="{% url 'school:student_edit_wizard' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}" 
                      hx-target="#dialog">
                <div class="mdc-button__ripple"></div>
                <span class="material-icons mdc-button__icon">person_add</span>
                <span class="mdc-button__label">Inscrire</span>
              </button>
            {% endif %}
          </div>
        </td>
      </tr>
      {% endfor %}
    {% endblock %}
  {% endwith %}
  {% endwith %}

  <!-- Include actions template -->
  {% include 'partials/student/actions.html' %}
</div>

<!-- Include JavaScript -->
{% include 'material/students/students_list_js.html' %}
