from django.urls import path
from . import views
from .password_reset_views import (
    MaterialPasswordResetView,
    MaterialPasswordResetVerifyView,
    MaterialPasswordResetDoneView,
    MaterialPasswordResetConfirmView,
    MaterialPasswordResetCompleteView
)

app_name = 'users'
urlpatterns = [
    path('login/', views.CustomLoginView.as_view(), name='login'),
    path('utilisateurs/', views.UsersListView.as_view(), name='users'),
    path('utilisateurs/ajouter/', views.UserCreateView.as_view(), name='user_add'),
    path('utilisateurs/<int:pk>/editer/', views.UserUpdateView.as_view(), name='user_edit'),
    path('utilisateurs/<int:pk>/changer-statut/', views.ToggleActiveStatusView.as_view(), name='change_active_status'),
    path('utilisateurs/<int:pk>/reinit-mot-de-passe/', views.ResetPasswordView.as_view(), name='reset_password'),
    path('utilisateurs/changer-mot-de-passe/', views.change_password, name='change_password'),
    path('utilisateurs/<int:user_id>/voir-mot-de-passe/', views.show_user_password, name='show_password'),

    # Password reset URLs with Material Design templates
    path('password_reset/', MaterialPasswordResetView.as_view(), name='password_reset'),
    path('password_reset/verify/', MaterialPasswordResetVerifyView.as_view(), name='password_reset_verify'),
    path('password_reset/confirm/', MaterialPasswordResetConfirmView.as_view(), name='password_reset_confirm'),
    path('password_reset/done/', MaterialPasswordResetDoneView.as_view(), name='password_reset_done'),
    path('password_reset/complete/', MaterialPasswordResetCompleteView.as_view(), name='password_reset_complete'),
]