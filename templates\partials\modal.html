{% load static %}
{% load widget_tweaks %}

{% with WIDGET_ERROR_CLASS='is-invalid' %}

<form action="{{ request.path }}{% if education %}?education={{ education }}{% endif %}{% if request.GET.cycle %}?cycle={{ request.GET.cycle }}{% endif %}" 
      hx-post="{{ request.path }}{% if education %}?education={{ education }}{% endif %}{% if request.GET.cycle %}?cycle={{ request.GET.cycle }}{% endif %}" 
      {% if custom_target %} hx-target="#{{ custom_target }}" hx-swap="outerHTML" {% endif %} method="post" 
      class="modal-content bg-light" id="modal-content" enctype="multipart/form-data"
      hx-on="
        htmx:beforeRequest: $('#modal-content').find('#submit-btn').html(`<span class='spinner-border spinner-border-sm mr-2' role='status' aria-hidden='true'></span>Exécution...`).prop('disabled', true);
        htmx:afterRequest: $('#modal-content').find(':submit').prop('disabled', false);  $('#modal-content').find(':submit').text('Valider');
    ">
    {% csrf_token %}
    <div class="modal-header text-success py-2">
        <h5 class="modal-title"> <span data-feather="chevron-right" class="align-middle"></span>{% block modal_title %} {% endblock %}</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="close-modal-icon">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="container mt-2">
        {% block modal_body %} {% endblock %}
    </div>

    <div class="modal-footer py-2">
        {% block modal_footer %} {% endblock %}
        <button class="btn btn-outline-secondary" data-dismiss="modal" id="close-modal-btn"
        formnovalidate="formnovalidate">
            <span data-feather="x" class="feather-16 align-middle"></span>
            Fermer
        </button>
    </div>

</form>
{% endwith %}

{% block js %}
<script>
    if(typeof(feather) !== undefined) feather.replace();
</script>
{% endblock %}