{% load widget_tweaks %}
{% load humanize %}

<!-- Material Design Data Table Component -->
<div class="mdc-card data-table-card mdc-card--outlined">
    <!-- Data Table Header with Actions -->
    <div class="data-table-header">
        <!-- Filter Toolbar -->
        <div class="filter-toolbar">
            {% if filters %}
                <button class="mdc-icon-button" aria-label="Filter">
                    <div class="mdc-icon-button__ripple"></div>
                    <span class="material-icons">filter_list</span>
                </button>
                
                <!-- Filter Chips -->
                <div class="mdc-chip-set" role="grid">
                    {% for filter in active_filters %}
                    <div class="mdc-chip" role="row">
                        <div class="mdc-chip__ripple"></div>
                        <span class="mdc-chip__icon mdc-chip__icon--leading material-icons">{{ filter.icon|default:"check_circle" }}</span>
                        <span class="mdc-chip__text">{{ filter.label }}</span>
                        <button class="mdc-chip__icon mdc-chip__icon--trailing material-icons" tabindex="-1">cancel</button>
                    </div>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <!-- Data Table Actions -->
        <div class="data-table-actions">
            {% if search_enabled %}
            <!-- Search Field -->
            <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon search-field">
                <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">search</span>
                <input id="data-table-search" class="mdc-text-field__input" type="text" 
                       placeholder="{{ search_placeholder|default:'Search...' }}"
                       value="{{ search|default:'' }}">
                <div class="mdc-notched-outline">
                    <div class="mdc-notched-outline__leading"></div>
                    <div class="mdc-notched-outline__notch">
                        <label class="mdc-floating-label">{{ search_label|default:'Search' }}</label>
                    </div>
                    <div class="mdc-notched-outline__trailing"></div>
                </div>
            </div>
            {% endif %}

            <!-- More Options Button -->
            <button class="mdc-icon-button" aria-label="More options">
                <div class="mdc-icon-button__ripple"></div>
                <span class="material-icons">more_vert</span>
            </button>
        </div>
    </div>

    <!-- Data Table -->
    <div class="mdc-data-table">
        <div class="mdc-data-table__table-container">
            <table class="mdc-data-table__table">
                <thead>
                    <tr class="mdc-data-table__header-row">
                        {% if checkbox_enabled %}
                        <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox">
                            <div class="mdc-checkbox mdc-data-table__header-row-checkbox">
                                <input type="checkbox" class="mdc-checkbox__native-control" 
                                       id="select-all" aria-label="Toggle all rows"/>
                                <div class="mdc-checkbox__background">
                                    <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                        <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                    </svg>
                                    <div class="mdc-checkbox__mixedmark"></div>
                                </div>
                            </div>
                        </th>
                        {% endif %}
                        
                        {% for column in columns %}
                        <th class="mdc-data-table__header-cell {% if column.sortable %}mdc-data-table__header-cell--with-sort{% endif %} {% if column.sticky %}sticky-column{% endif %}" 
                            {% if column.sortable %}role="columnheader" scope="col" aria-sort="none"{% endif %}
                            {% if column.width %}style="width: {{ column.width }}"{% endif %}
                            {% if column.sortable %}
                                hx-get="{{ request.path }}" 
                                hx-include="[name='per_page']"
                                hx-vals='{"sort": "{% if sort_field == column.sort_field and sort_direction == "asc" %}-{% endif %}{{ column.sort_field }}"}'
                                hx-target="{{ table_target|default:'#app-content' }}"
                            {% endif %}>
                            {% if column.sortable %}
                            <div class="mdc-data-table__header-cell-wrapper">
                                <div class="mdc-data-table__header-cell-label">{{ column.label }}</div>
                                <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" 
                                        aria-label="Sort by {{ column.label }}">
                                    {% if sort_field == column.sort_field %}
                                        {% if sort_direction == 'asc' %}arrow_upward{% else %}arrow_downward{% endif %}
                                    {% else %}
                                        arrow_upward
                                    {% endif %}
                                </button>
                            </div>
                            {% else %}
                                {{ column.label }}
                            {% endif %}
                        </th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody class="mdc-data-table__content">
                    {% block table_body %}
                    <!-- Table body content will be provided by the including template -->
                    {% endblock %}
                </tbody>
            </table>
        </div>

        <!-- Data Table Pagination -->
        {% if pagination_enabled %}
        <div class="mdc-data-table__pagination">
            <div class="mdc-data-table__pagination-trailing">
                <div class="mdc-data-table__pagination-rows-per-page">
                    <div class="mdc-data-table__pagination-rows-per-page-label">
                        Lignes par page
                    </div>

                    <div class="mdc-select mdc-select--outlined mdc-select--no-label mdc-data-table__pagination-rows-per-page-select">
                        <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                            <span class="mdc-select__selected-text">{{ per_page|default:"10" }}</span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <span class="mdc-notched-outline">
                                <span class="mdc-notched-outline__leading"></span>
                                <span class="mdc-notched-outline__trailing"></span>
                            </span>
                        </div>

                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-list" role="listbox">
                                <li class="mdc-list-item {% if per_page == '10' %}mdc-list-item--selected{% endif %}"
                                    aria-selected="{% if per_page == '10' %}true{% else %}false{% endif %}"
                                    role="option" data-value="10"
                                    hx-get="{{ request.path }}"
                                    hx-target="{{ table_target|default:'#app-content' }}"
                                    hx-vals='{"per_page": "10"}'>
                                    <span class="mdc-list-item__text">10</span>
                                </li>
                                <li class="mdc-list-item {% if per_page == '25' %}mdc-list-item--selected{% endif %}"
                                    aria-selected="{% if per_page == '25' %}true{% else %}false{% endif %}"
                                    role="option" data-value="25"
                                    hx-get="{{ request.path }}"
                                    hx-target="{{ table_target|default:'#app-content' }}"
                                    hx-vals='{"per_page": "25"}'>
                                    <span class="mdc-list-item__text">25</span>
                                </li>
                                <li class="mdc-list-item {% if per_page == '50' %}mdc-list-item--selected{% endif %}"
                                    aria-selected="{% if per_page == '50' %}true{% else %}false{% endif %}"
                                    role="option" data-value="50"
                                    hx-get="{{ request.path }}"
                                    hx-target="{{ table_target|default:'#app-content' }}"
                                    hx-vals='{"per_page": "50"}'>
                                    <span class="mdc-list-item__text">50</span>
                                </li>
                                <li class="mdc-list-item {% if per_page == '100' %}mdc-list-item--selected{% endif %}"
                                    aria-selected="{% if per_page == '100' %}true{% else %}false{% endif %}"
                                    role="option" data-value="100"
                                    hx-get="{{ request.path }}"
                                    hx-target="{{ table_target|default:'#app-content' }}"
                                    hx-vals='{"per_page": "100"}'>
                                    <span class="mdc-list-item__text">100</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="mdc-data-table__pagination-navigation">
                    {% if page_obj %}
                    <div class="mdc-data-table__pagination-total">
                        {{ page_obj.start_index }}-{{ page_obj.end_index }} de {{ page_obj.paginator.count }}
                    </div>
                    <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" 
                            data-page="first" {% if not page_obj.has_previous %}disabled{% endif %}
                            {% if page_obj.has_previous %}
                                hx-get="{{ request.path }}"
                                hx-target="{{ table_target|default:'#app-content' }}"
                                hx-vals='{"page": "1"}'
                            {% endif %}>
                        <div class="mdc-button__icon">first_page</div>
                    </button>
                    <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" 
                            data-page="prev" {% if not page_obj.has_previous %}disabled{% endif %}
                            {% if page_obj.has_previous %}
                                hx-get="{{ request.path }}"
                                hx-target="{{ table_target|default:'#app-content' }}"
                                hx-vals='{"page": "{{ page_obj.previous_page_number }}"}'
                            {% endif %}>
                        <div class="mdc-button__icon">chevron_left</div>
                    </button>
                    <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" 
                            data-page="next" {% if not page_obj.has_next %}disabled{% endif %}
                            {% if page_obj.has_next %}
                                hx-get="{{ request.path }}"
                                hx-target="{{ table_target|default:'#app-content' }}"
                                hx-vals='{"page": "{{ page_obj.next_page_number }}"}'
                            {% endif %}>
                        <div class="mdc-button__icon">chevron_right</div>
                    </button>
                    <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" 
                            data-page="last" {% if not page_obj.has_next %}disabled{% endif %}
                            {% if page_obj.has_next %}
                                hx-get="{{ request.path }}"
                                hx-target="{{ table_target|default:'#app-content' }}"
                                hx-vals='{"page": "{{ page_obj.paginator.num_pages }}"}'
                            {% endif %}>
                        <div class="mdc-button__icon">last_page</div>
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
