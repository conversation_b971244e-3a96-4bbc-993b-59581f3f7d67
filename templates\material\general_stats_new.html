{% load static %}
{% load humanize %}

{% block page_title %}{{ page_title }} | EcolePro{% endblock %}

{% block content %}
{% if user.is_authenticated and perms.school.view_student and user.role != 'TC' %}
<!-- Dashboard Header with Welcome Message -->
<div class="dashboard-header">
    <div class="welcome-section">
        <div class="welcome-text">
            <h1>Bienvenue, {{ user.first_name|default:user.username }}</h1>
            <p class="subtitle">Voici un aperçu de votre école aujourd'hui</p>
        </div>
        <!-- <div class="date-display">
            <div class="date-card">
                <span class="material-icons">today</span>
                <div class="date-info">
                    <span class="day">{{ current_date|date:"d"|default:"01" }}</span>
                    <span class="month-year">{{ current_date|date:"F Y"|default:"Janvier 2025" }}</span>
                </div>
            </div>
        </div> -->
    </div>

    {% if remaining_days %}
    <div class="mdc-banner" role="banner">
        <div class="mdc-banner__content" role="alertdialog" aria-live="assertive">
            <div class="mdc-banner__graphic-text-wrapper">
                <div class="mdc-banner__graphic">
                    <span class="material-icons">timer</span>
                </div>
                <div class="mdc-banner__text">
                    Vous utilisez la version d'éssai de l'application EcolePro. Il vous reste <strong>{{ remaining_days }} jours</strong> d'utilisation gratuite.
                </div>
            </div>
            <div class="mdc-banner__actions">
                <button type="button" class="mdc-button mdc-banner__primary-action">
                    <div class="mdc-button__ripple"></div>
                    <div class="mdc-button__label">Compris</div>
                </button>
                <button type="button" class="mdc-button mdc-banner__secondary-action">
                    <div class="mdc-button__ripple"></div>
                    <div class="mdc-button__label">Passer à la version Pro</div>
                </button>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Quick Stats Cards -->
<div class="quick-stats-container">
    <div class="mdc-card quick-stat-card">
        <div class="quick-stat-content" style="display: flex !important">
            <div class="quick-stat-icon blue-gradient">
                <span class="material-icons">groups</span>
            </div>
            <div class="quick-stat-data text-center">
                <h3 class="mdc-fw-bold">{{ data.boys|intcomma|default:"0" }}</h3>
                <p>Garçons</p>
            </div>
            <div class="quick-stat-data text-center">
                <h3 class="mdc-fw-bold">{{ data.girls|intcomma|default:"0" }}</h3>
                <p>Filles</p>
            </div>
            <div class="quick-stat-data text-center">
                <h3 class="mdc-fw-bold">{{ data.students|intcomma|default:"0" }}</h3>
                <p>Élèves</p>
            </div>
        </div>
        <div class="quick-stat-footer" style="display: flex; justify-content: space-between;">
            <h3>EFFECTIFS</h3>
            <button class="mdc-button mdc-button--icon-leading">
                <span class="mdc-button__ripple"></span>
                <i class="material-icons mdc-button__icon" aria-hidden="true"
                  >arrow_forward</i
                >
                <span class="mdc-button__label">Voir la liste</span>
              </button>
              
        </div>
    </div>
    <div class="mdc-card quick-stat-card">
        <div class="quick-stat-content" style="display: flex !important">
            <div class="quick-stat-icon blue-gradient">
                <span class="material-icons">groups</span>
            </div>
            <div class="quick-stat-data text-center">
                <h3 class="mdc-fw-bold">{{ data.preschool|intcomma|default:"0" }}</h3>
                <p>Matle</p>
            </div>
            <div class="quick-stat-data text-center">
                <h3 class="mdc-fw-bold">{{ data.students_primary|intcomma|default:"0" }}</h3>
                <p>Primaire</p>
            </div>
            <div class="quick-stat-data text-center">
                <h3 class="mdc-fw-bold">{{ data.students_secondary|intcomma|default:"0" }}</h3>
                <p>Secondaire</p>
            </div>
        </div>
        <div class="quick-stat-footer" style="display: flex; justify-content: space-between;">
            <h3>PAR CYCLE</h3>
            <button class="mdc-button mdc-button--icon-leading">
                <span class="mdc-button__ripple"></span>
                <i class="material-icons mdc-button__icon" aria-hidden="true"
                  >arrow_forward</i
                >
                <span class="mdc-button__label">Voir la liste</span>
              </button>
              
        </div>
    </div>


    <div class="mdc-card quick-stat-card">
        <div class="quick-stat-content">
            <div class="quick-stat-icon green-gradient">
                <span class="material-icons">class</span>
            </div>
            <div class="quick-stat-data">
                <h3>{{ levels_count|intcomma|default:"0" }}</h3>
                <p>Classes</p>
            </div>
        </div>
        <div class="quick-stat-footer">
            <span class="trend-indicator positive">
                <span class="material-icons">trending_up</span>
                <span>1.2%</span>
            </span>
            <span class="period">depuis le mois dernier</span>
        </div>
    </div>

    {% if user.role == ROLE_ACCOUNTANT or user.role == ROLE_FOUNDER %}
    <div class="mdc-card quick-stat-card">
        <div class="quick-stat-content">
            <div class="quick-stat-icon amber-gradient">
                <span class="material-icons">payments</span>
            </div>
            <div class="quick-stat-data">
                <h3>{{ data.total_paid_today|intcomma|default:"0" }} F</h3>
                <p>Paiements aujourd'hui</p>
            </div>
        </div>
        <div class="quick-stat-footer">
            <span class="trend-indicator {% if data.total_paid_today > data.total_paid_yesterday %}positive{% elif data.total_paid_today < data.total_paid_yesterday %}negative{% else %}neutral{% endif %}">
                <span class="material-icons">
                    {% if data.total_paid_today > data.total_paid_yesterday %}
                    trending_up
                    {% elif data.total_paid_today < data.total_paid_yesterday %}
                    trending_down
                    {% else %}
                    trending_flat
                    {% endif %}
                </span>
                <span>par rapport à hier</span>
            </span>
        </div>
    </div>
    {% endif %}
</div>

<!-- Main Dashboard Content -->
<div class="dashboard-main-content">
    <!-- Left Column -->
    <div class="dashboard-column dashboard-column-left">
        <!-- Student Gender Distribution Chart -->
        <div class="mdc-card chart-card">
            <div class="chart-card-header">
                <h2>Répartition des élèves</h2>
                <div class="chart-actions">
                    <button class="mdc-icon-button" aria-label="More options">
                        <div class="mdc-icon-button__ripple"></div>
                        <span class="material-icons">more_vert</span>
                    </button>
                </div>
            </div>
            <div class="chart-card-content">
                <div class="chart-container">
                    <canvas id="genderDistributionChart"></canvas>
                </div>
                <div class="chart-legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #1976d2;"></div>
                        <div class="legend-label">Garçons: {{ data.boys|intcomma|default:"0" }}</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #e91e63;"></div>
                        <div class="legend-label">Filles: {{ data.girls|intcomma|default:"0" }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Cards -->
        {% if user.is_authenticated and perms.school.view_student and user.role != 'TC' %}
        <div class="action-cards-container">
            <h2 class="section-title">Actions rapides</h2>
            <div class="action-cards">
                {% if levels_count == 0 %}
                <div class="mdc-card action-card">
                    <div class="action-card-content">
                        <div class="action-card-icon">
                            <span class="material-icons">class</span>
                        </div>
                        <div class="action-card-text">
                            <h3>Créez des classes</h3>
                            <p>Vous devez créer des classes avant d'inscrire les élèves.</p>
                        </div>
                    </div>
                    <div class="action-card-actions">
                        <a href="{% url 'school:levels' %}"
                           hx-get="{% url 'school:levels' %}"
                           hx-push-url="{% url 'school:levels' %}"
                           hx-target="#app-content"
                           class="mdc-button mdc-button--raised">
                            <span class="mdc-button__ripple"></span>
                            <span class="mdc-button__label">Créer</span>
                        </a>
                    </div>
                </div>
                {% endif %}

                {% if not user.school.logo %}
                <div class="mdc-card action-card" id="logo-card">
                    <div class="action-card-content">
                        <div class="action-card-icon">
                            <span class="material-icons">image</span>
                        </div>
                        <div class="action-card-text">
                            <h3>Logo de l'école</h3>
                            <p>Ajoutez le logo de votre école pour personnaliser vos documents.</p>
                        </div>
                    </div>
                    <div class="action-card-actions">
                        <button type="button" class="mdc-button mdc-button--outlined" id="upload-logo-button">
                            <span class="mdc-button__ripple"></span>
                            <span class="material-icons mdc-button__icon">upload</span>
                            <span class="mdc-button__label">Télécharger</span>
                        </button>
                        <button type="button" class="mdc-button" onclick="dismissCard('logo-card')">
                            <span class="mdc-button__ripple"></span>
                            <span class="mdc-button__label">Plus tard</span>
                        </button>
                    </div>
                </div>
                {% endif %}

                {% if not user.photo %}
                <div class="mdc-card action-card" id="photo-card">
                    <div class="action-card-content">
                        <div class="action-card-icon">
                            <span class="material-icons">account_circle</span>
                        </div>
                        <div class="action-card-text">
                            <h3>Photo de profil</h3>
                            <p>Ajoutez votre photo de profil pour personnaliser votre compte.</p>
                        </div>
                    </div>
                    <div class="action-card-actions">
                        <button type="button" class="mdc-button mdc-button--outlined" id="upload-photo-button">
                            <span class="mdc-button__ripple"></span>
                            <span class="material-icons mdc-button__icon">upload</span>
                            <span class="mdc-button__label">Télécharger</span>
                        </button>
                        <button type="button" class="mdc-button" onclick="dismissCard('photo-card')">
                            <span class="mdc-button__ripple"></span>
                            <span class="mdc-button__label">Plus tard</span>
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Right Column -->
    <div class="dashboard-column dashboard-column-right">
        {% if user.role == ROLE_ACCOUNTANT or user.role == ROLE_FOUNDER %}
        <!-- Payment Chart -->
        <div class="mdc-card chart-card">
            <div class="chart-card-header">
                <h2>Paiements mensuels</h2>
                <div class="chart-period-selector">
                    <div class="mdc-chip-set" role="grid">
                        <div class="mdc-chip" role="row">
                            <div class="mdc-chip__ripple"></div>
                            <span class="mdc-chip__text">Mois</span>
                        </div>
                        <div class="mdc-chip mdc-chip--selected" role="row">
                            <div class="mdc-chip__ripple"></div>
                            <span class="mdc-chip__text">Trimestre</span>
                        </div>
                        <div class="mdc-chip" role="row">
                            <div class="mdc-chip__ripple"></div>
                            <span class="mdc-chip__text">Année</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="chart-card-content">
                <div class="chart-container">
                    <canvas id="paymentsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Payment Summary -->
        <div class="mdc-card summary-card">
            <div class="summary-card-header">
                <h2>Résumé des paiements {{ year }}</h2>
            </div>
            <div class="summary-card-content">
                <div class="summary-item">
                    <div class="summary-label">Frais d'inscription</div>
                    <div class="summary-value">{{ data.inscription|intcomma|default:"0" }} F</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">Scolarité</div>
                    <div class="summary-value">{{ data.scolarite|intcomma|default:"0" }} F</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">Annexe</div>
                    <div class="summary-value">{{ data.annexe|intcomma|default:"0" }} F</div>
                </div>
                <div class="summary-divider"></div>
                <div class="summary-item summary-total">
                    <div class="summary-label">Total</div>
                    <div class="summary-value">{{ data.total_paid|intcomma|default:"0" }} F</div>
                </div>
            </div>
            <div class="summary-card-actions">
                <a href="#" class="mdc-button">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Voir tous les paiements</span>
                </a>
            </div>
        </div>
        {% endif %}

        <!-- Recent Activities -->
        <div class="mdc-card activities-card">
            <div class="activities-card-header">
                <h2>Activités récentes</h2>
                <a href="#" class="view-all-link">Voir tout</a>
            </div>
            <div class="activities-card-content">
                <div class="activity-item">
                    <div class="activity-icon green">
                        <span class="material-icons">person_add</span>
                    </div>
                    <div class="activity-details">
                        <div class="activity-title">Nouvel élève inscrit</div>
                        <div class="activity-description">Amadou Diallo a été inscrit en 6EME 1</div>
                        <div class="activity-time">Il y a 2 heures</div>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon blue">
                        <span class="material-icons">payments</span>
                    </div>
                    <div class="activity-details">
                        <div class="activity-title">Paiement reçu</div>
                        <div class="activity-description">25 000 F reçus pour Fatou Sow (5EME 2)</div>
                        <div class="activity-time">Il y a 3 heures</div>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon amber">
                        <span class="material-icons">edit</span>
                    </div>
                    <div class="activity-details">
                        <div class="activity-title">Note modifiée</div>
                        <div class="activity-description">Note de Mathématiques modifiée pour Moussa Camara</div>
                        <div class="activity-time">Il y a 5 heures</div>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon purple">
                        <span class="material-icons">school</span>
                    </div>
                    <div class="activity-details">
                        <div class="activity-title">Nouvel enseignant</div>
                        <div class="activity-description">M. Ousmane Diop a été ajouté comme enseignant</div>
                        <div class="activity-time">Il y a 1 jour</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Bar -->
<div class="search-section">
    <div class="mdc-card search-card">
        <div class="search-card-content">
            <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon search-field">
                <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">search</span>
                <input type="search" class="mdc-text-field__input" id="search" name="search"
                       placeholder="Rechercher un élève par nom ou par matricule"
                       hx-get="{% if user.role == 'CS' %}{% url 'school:active_students' %}{% else %}{% url 'school:students' %}?statut=inscrits{% endif %}"
                       hx-target="#app-content"
                       hx-push-url="{% if user.role == 'CS' %}{% url 'school:active_students' %}{% else %}{% url 'school:students' %}?statut=inscrits{% endif %}"
                       hx-trigger="keyup[keyCode==13]">
                <div class="mdc-notched-outline">
                    <div class="mdc-notched-outline__leading"></div>
                    <div class="mdc-notched-outline__notch">
                        <label class="mdc-floating-label" for="search">Rechercher</label>
                    </div>
                    <div class="mdc-notched-outline__trailing"></div>
                </div>
            </div>
            <button class="mdc-button mdc-button--raised search-button"
                    hx-get="{% if user.role == 'CS' %}{% url 'school:active_students' %}{% else %}{% url 'school:students' %}?statut=inscrits{% endif %}"
                    hx-target="#app-content"
                    hx-push-url="{% if user.role == 'CS' %}{% url 'school:active_students' %}{% else %}{% url 'school:students' %}?statut=inscrits{% endif %}"
                    hx-include="[name=search]">
                <span class="mdc-button__ripple"></span>
                <span class="material-icons mdc-button__icon">search</span>
                <span class="mdc-button__label">Rechercher</span>
            </button>
        </div>
    </div>
</div>

{% else %}
    {% include "partials/teacher/teacher_menu.html" with subtitle=subtitle %}
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard Layout */
    .dashboard-header {
        margin-bottom: var(--md-spacing-4);
    }

    .welcome-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--md-spacing-3);
    }

    .welcome-text h1 {
        font-size: 1.75rem;
        margin-bottom: var(--md-spacing-1);
        color: var(--md-on-surface);
    }

    .welcome-text .subtitle {
        color: var(--md-on-surface-variant);
        font-size: 1rem;
        margin: 0;
    }

    .date-card {
        display: flex;
        align-items: center;
        background-color: var(--md-surface);
        padding: var(--md-spacing-2);
        border-radius: var(--md-border-radius-medium);
        box-shadow: var(--md-elevation-level1);
    }

    .date-card .material-icons {
        color: var(--md-primary);
        margin-right: var(--md-spacing-2);
        font-size: 1.5rem;
    }

    .date-info {
        display: flex;
        flex-direction: column;
    }

    .date-info .day {
        font-size: 1.5rem;
        font-weight: var(--md-font-weight-medium);
        color: var(--md-on-surface);
    }

    .date-info .month-year {
        font-size: 0.875rem;
        color: var(--md-on-surface-variant);
    }

    /* Quick Stats */
    .quick-stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: var(--md-spacing-3);
        margin-bottom: var(--md-spacing-4);
    }

    .quick-stat-card {
        padding: var(--md-spacing-2);
        border-radius: var(--md-border-radius-medium);
        transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                    transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .quick-stat-card:hover {
        box-shadow: var(--md-elevation-level2);
        transform: translateY(-2px);
    }

    .quick-stat-content {
        display: flex;
        align-items: center;
        gap: var(--md-spacing-2);
        margin-bottom: var(--md-spacing-2);
    }

    .quick-stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

    .blue-gradient {
        background: linear-gradient(135deg, #1976d2, #64b5f6);
    }

    .purple-gradient {
        background: linear-gradient(135deg, #9c27b0, #ba68c8);
    }

    .green-gradient {
        background: linear-gradient(135deg, #4caf50, #81c784);
    }

    .amber-gradient {
        background: linear-gradient(135deg, #ff9800, #ffb74d);
    }

    .quick-stat-data {
        flex: 1;
    }

    .quick-stat-data h3 {
        font-size: 1.5rem;
        font-weight: var(--md-font-weight-medium);
        margin: 0 0 var(--md-spacing-1) 0;
        color: var(--md-on-surface);
    }

    .quick-stat-data p {
        font-size: 0.875rem;
        color: var(--md-on-surface-variant);
        margin: 0;
    }
    
    .text-center {
        text-align: center;
    }

    .quick-stat-footer {
        display: flex;
        align-items: center;
        gap: var(--md-spacing-1);
        font-size: 0.75rem;
        color: var(--md-on-surface-variant);
        padding-top: var(--md-spacing-1);
        border-top: 1px solid var(--md-outline-variant);
    }

    .period {
        margin-left: auto;
    }

    /* Main Dashboard Content */
    .dashboard-main-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--md-spacing-4);
        margin-bottom: var(--md-spacing-4);
    }

    /* Chart Cards */
    .chart-card {
        margin-bottom: var(--md-spacing-3);
        border-radius: var(--md-border-radius-medium);
        overflow: hidden;
    }

    .chart-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--md-spacing-2);
        border-bottom: 1px solid var(--md-outline-variant);
    }

    .chart-card-header h2 {
        font-size: 1.125rem;
        font-weight: var(--md-font-weight-medium);
        margin: 0;
        color: var(--md-on-surface);
    }

    .chart-actions {
        display: flex;
        gap: var(--md-spacing-1);
    }

    .chart-card-content {
        padding: var(--md-spacing-2);
    }

    .chart-container {
        height: 250px;
        position: relative;
    }

    .chart-legend {
        display: flex;
        justify-content: center;
        gap: var(--md-spacing-3);
        margin-top: var(--md-spacing-2);
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: var(--md-spacing-1);
    }

    .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }

    .legend-label {
        font-size: 0.875rem;
        color: var(--md-on-surface-variant);
    }

    .chart-period-selector {
        display: flex;
        align-items: center;
    }

    /* Action Cards */
    .action-cards-container {
        margin-bottom: var(--md-spacing-3);
    }

    .section-title {
        font-size: 1.125rem;
        font-weight: var(--md-font-weight-medium);
        margin-bottom: var(--md-spacing-2);
        color: var(--md-on-surface-variant);
    }

    .action-cards {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
        gap: var(--md-spacing-2);
    }

    .action-card {
        border-radius: var(--md-border-radius-medium);
        overflow: hidden;
    }

    .action-card-content {
        display: flex;
        padding: var(--md-spacing-2);
        gap: var(--md-spacing-2);
    }

    .action-card-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--md-primary-container);
        color: var(--md-on-primary-container);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .action-card-text {
        flex: 1;
    }

    .action-card-text h3 {
        font-size: 1rem;
        font-weight: var(--md-font-weight-medium);
        margin: 0 0 var(--md-spacing-1) 0;
        color: var(--md-on-surface);
    }

    .action-card-text p {
        font-size: 0.875rem;
        color: var(--md-on-surface-variant);
        margin: 0;
    }

    .action-card-actions {
        display: flex;
        justify-content: flex-end;
        gap: var(--md-spacing-1);
        padding: var(--md-spacing-1) var(--md-spacing-2);
        border-top: 1px solid var(--md-outline-variant);
    }

    /* Summary Card */
    .summary-card {
        margin-bottom: var(--md-spacing-3);
        border-radius: var(--md-border-radius-medium);
        overflow: hidden;
    }

    .summary-card-header {
        padding: var(--md-spacing-2);
        border-bottom: 1px solid var(--md-outline-variant);
    }

    .summary-card-header h2 {
        font-size: 1.125rem;
        font-weight: var(--md-font-weight-medium);
        margin: 0;
        color: var(--md-on-surface);
    }

    .summary-card-content {
        padding: var(--md-spacing-2);
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--md-spacing-1) 0;
    }

    .summary-label {
        font-size: 0.875rem;
        color: var(--md-on-surface-variant);
    }

    .summary-value {
        font-size: 0.875rem;
        font-weight: var(--md-font-weight-medium);
        color: var(--md-on-surface);
    }

    .summary-divider {
        height: 1px;
        background-color: var(--md-outline-variant);
        margin: var(--md-spacing-1) 0;
    }

    .summary-total {
        font-weight: var(--md-font-weight-medium);
    }

    .summary-total .summary-label,
    .summary-total .summary-value {
        font-size: 1rem;
        color: var(--md-on-surface);
    }

    .summary-card-actions {
        padding: var(--md-spacing-1) var(--md-spacing-2);
        border-top: 1px solid var(--md-outline-variant);
        display: flex;
        justify-content: flex-end;
    }

    /* Activities Card */
    .activities-card {
        border-radius: var(--md-border-radius-medium);
        overflow: hidden;
    }

    .activities-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--md-spacing-2);
        border-bottom: 1px solid var(--md-outline-variant);
    }

    .activities-card-header h2 {
        font-size: 1.125rem;
        font-weight: var(--md-font-weight-medium);
        margin: 0;
        color: var(--md-on-surface);
    }

    .view-all-link {
        font-size: 0.875rem;
        color: var(--md-primary);
        text-decoration: none;
    }

    .activities-card-content {
        padding: var(--md-spacing-1);
    }

    .activity-item {
        display: flex;
        gap: var(--md-spacing-2);
        padding: var(--md-spacing-2);
        border-bottom: 1px solid var(--md-outline-variant);
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

    .activity-icon.green {
        background-color: #4caf50;
    }

    .activity-icon.blue {
        background-color: #1976d2;
    }

    .activity-icon.amber {
        background-color: #ff9800;
    }

    .activity-icon.purple {
        background-color: #9c27b0;
    }

    .activity-details {
        flex: 1;
    }

    .activity-title {
        font-size: 0.875rem;
        font-weight: var(--md-font-weight-medium);
        margin-bottom: 2px;
        color: var(--md-on-surface);
    }

    .activity-description {
        font-size: 0.8125rem;
        color: var(--md-on-surface-variant);
        margin-bottom: 4px;
    }

    .activity-time {
        font-size: 0.75rem;
        color: var(--md-on-surface-variant);
    }

    /* Search Section */
    .search-section {
        margin-bottom: var(--md-spacing-4);
    }

    .search-card {
        border-radius: var(--md-border-radius-medium);
        overflow: hidden;
    }

    .search-card-content {
        display: flex;
        gap: var(--md-spacing-2);
        padding: var(--md-spacing-2);
    }

    .search-field {
        flex: 1;
    }

    /* Responsive Adjustments */
    @media (max-width: 1024px) {
        .dashboard-main-content {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 768px) {
        .welcome-section {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--md-spacing-2);
        }

        .quick-stats-container {
            grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
        }

        .search-card-content {
            flex-direction: column;
        }

        .search-button {
            align-self: flex-end;
        }
    }
</style>
{% endblock %}

{% block extra_js %}

<script>
    function initializeCharts() {
        const genderChartElement = document.getElementById('genderDistributionChart');
        if (genderChartElement) {
            const boysCount = parseInt("{{ data.boys|default:'0' }}");
            const girlsCount = parseInt("{{ data.girls|default:'0' }}");

            new Chart(genderChartElement, {
                type: 'doughnut',
                data: {
                    labels: ['Garçons', 'Filles'],
                    datasets: [{
                        data: [boysCount, girlsCount],
                        backgroundColor: ['#1976d2', '#e91e63'],
                        borderWidth: 0,
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Payments Chart
        const paymentsChartElement = document.getElementById('paymentsChart');
        if (paymentsChartElement) {
            new Chart(paymentsChartElement, {
                type: 'bar',
                data: {
                    labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'],
                    datasets: [
                        {
                            label: 'Inscription',
                            data: [65000, 45000, 30000, 25000, 20000, 15000, 10000, 5000, 120000, 40000, 30000, 20000],
                            backgroundColor: '#1976d2',
                            borderRadius: 4
                        },
                        {
                            label: 'Scolarité',
                            data: [120000, 110000, 100000, 95000, 90000, 85000, 80000, 75000, 150000, 140000, 130000, 120000],
                            backgroundColor: '#4caf50',
                            borderRadius: 4
                        },
                        {
                            label: 'Annexe',
                            data: [30000, 25000, 20000, 15000, 10000, 5000, 5000, 5000, 35000, 30000, 25000, 20000],
                            backgroundColor: '#ff9800',
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString() + ' F';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                            align: 'end',
                            labels: {
                                boxWidth: 12,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += context.parsed.y.toLocaleString() + ' F';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });
        }

    }

    document.body.addEventListener('htmx:afterSwap', function(){
        initializeCharts()
    })
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Material Components
        const textFields = document.querySelectorAll('.mdc-text-field');
        textFields.forEach(textField => {
            mdc.textField.MDCTextField.attachTo(textField);
        });

        // Initialize banner if present
        const bannerElement = document.querySelector('.mdc-banner');
        if (bannerElement) {
            const banner = new mdc.banner.MDCBanner(bannerElement);
            banner.open();
        }

        // Initialize chips
        const chipSetElements = document.querySelectorAll('.mdc-chip-set');
        chipSetElements.forEach(chipSetElement => {
            mdc.chips.MDCChipSet.attachTo(chipSetElement);
        });

        // Gender Distribution Chart
        initializeCharts();

        // File upload handlers
        const uploadLogoButton = document.getElementById('upload-logo-button');
        if (uploadLogoButton) {
            uploadLogoButton.addEventListener('click', function() {
                // Create a file input element
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = 'image/*';
                fileInput.style.display = 'none';
                document.body.appendChild(fileInput);

                // Trigger click on the file input
                fileInput.click();

                // Handle file selection
                fileInput.addEventListener('change', function() {
                    if (fileInput.files && fileInput.files[0]) {
                        // Here you would normally upload the file to the server
                        // For now, we'll just show a success message
                        const snackbar = document.createElement('div');
                        snackbar.className = 'mdc-snackbar';
                        snackbar.innerHTML = `
                            <div class="mdc-snackbar__surface" role="status" aria-relevant="additions">
                                <div class="mdc-snackbar__label" aria-atomic="false">
                                    Logo téléchargé avec succès!
                                </div>
                                <div class="mdc-snackbar__actions" aria-atomic="true">
                                    <button type="button" class="mdc-button mdc-snackbar__action">
                                        <div class="mdc-button__ripple"></div>
                                        <span class="mdc-button__label">OK</span>
                                    </button>
                                </div>
                            </div>
                        `;
                        document.body.appendChild(snackbar);

                        const mdcSnackbar = new mdc.snackbar.MDCSnackbar(snackbar);
                        mdcSnackbar.open();

                        // Remove the file input
                        document.body.removeChild(fileInput);
                    }
                });
            });
        }

        const uploadPhotoButton = document.getElementById('upload-photo-button');
        if (uploadPhotoButton) {
            uploadPhotoButton.addEventListener('click', function() {
                // Create a file input element
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = 'image/*';
                fileInput.style.display = 'none';
                document.body.appendChild(fileInput);

                // Trigger click on the file input
                fileInput.click();

                // Handle file selection
                fileInput.addEventListener('change', function() {
                    if (fileInput.files && fileInput.files[0]) {
                        // Here you would normally upload the file to the server
                        // For now, we'll just show a success message
                        const snackbar = document.createElement('div');
                        snackbar.className = 'mdc-snackbar';
                        snackbar.innerHTML = `
                            <div class="mdc-snackbar__surface" role="status" aria-relevant="additions">
                                <div class="mdc-snackbar__label" aria-atomic="false">
                                    Photo téléchargée avec succès!
                                </div>
                                <div class="mdc-snackbar__actions" aria-atomic="true">
                                    <button type="button" class="mdc-button mdc-snackbar__action">
                                        <div class="mdc-button__ripple"></div>
                                        <span class="mdc-button__label">OK</span>
                                    </button>
                                </div>
                            </div>
                        `;
                        document.body.appendChild(snackbar);

                        const mdcSnackbar = new mdc.snackbar.MDCSnackbar(snackbar);
                        mdcSnackbar.open();

                        // Remove the file input
                        document.body.removeChild(fileInput);
                    }
                });
            });
        }
    });

    // Function to dismiss cards and save preference
    function dismissCard(cardId) {
        const card = document.getElementById(cardId);
        if (card) {
            card.style.display = 'none';
            localStorage.setItem(cardId + 'Preference', 'false');
        }
    }

    // Check for saved preferences
    document.addEventListener('DOMContentLoaded', function() {
        const logoCardPreference = localStorage.getItem('logo-cardPreference');
        if (logoCardPreference === 'false') {
            const logoCard = document.getElementById('logo-card');
            if (logoCard) {
                logoCard.style.display = 'none';
            }
        }

        const photoCardPreference = localStorage.getItem('photo-cardPreference');
        if (photoCardPreference === 'false') {
            const photoCard = document.getElementById('photo-card');
            if (photoCard) {
                photoCard.style.display = 'none';
            }
        }
    });
</script>
{% endblock %}