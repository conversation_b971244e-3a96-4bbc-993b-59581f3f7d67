from django.shortcuts import render
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Sum
from .models import Student, Enrollment, Teacher, Payment
from school.views import get_session_year, StudentsListViewBase
from datetime import datetime

class MaterialDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'material/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        year = get_session_year(self.request)
        
        # Get statistics
        context['total_students'] = Enrollment.objects.filter(
            school=user.school, 
            year=year, 
            active=True
        ).count()
        
        context['total_teachers'] = Teacher.objects.filter(
            school=user.school,
            active=True
        ).count()
        
        context['total_classes'] = Enrollment.objects.filter(
            school=user.school,
            year=year
        ).values('level_fr').distinct().count()
        
        # Get recent payments
        context['recent_payments'] = Payment.objects.filter(
            enrollment__school=user.school
        ).order_by('-created_at')[:10]
        
        # Calculate total payments for the current month
        from datetime import datetime
        current_month = datetime.now().month
        current_year = datetime.now().year
        
        total_payments = Payment.objects.filter(
            enrollment__school=user.school,
            created_at__month=current_month,
            created_at__year=current_year
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        context['total_payments'] = total_payments
        
        # Mock recent activities (in a real app, this would come from a model)
        context['recent_activities'] = [
            {
                'icon': 'person_add',
                'title': 'Nouvel élève inscrit',
                'description': 'John Doe a été inscrit en classe de 6ème',
                'timestamp': datetime.now()
            },
            {
                'icon': 'payment',
                'title': 'Paiement reçu',
                'description': 'Paiement de 50 000 FCFA reçu pour Jane Smith',
                'timestamp': datetime.now()
            },
            {
                'icon': 'school',
                'title': 'Nouvelle classe créée',
                'description': 'La classe de 3ème B a été créée',
                'timestamp': datetime.now()
            }
        ]

        return context


class MaterialStudentsListView(StudentsListViewBase):
    """Material Design version of the students list view"""
    model = Enrollment
    template_name = 'material/students/students_main.html'
    context_object_name = 'enrollments'
    permission_required = 'school.view_payment'

    def get_template_names(self):
        """Return different templates for HTMX vs full requests"""
        if self.request.htmx:
            return ['material/students/students_list_partial.html']
        return [self.template_name]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = self.get_year()

        context['page_title'] = 'Liste des élèves'
        context['page'] = self.request.GET.get('page') or ''
        context['year'] = str(year)

        # Search functionality
        if self.request.GET.get('search'):
            context['search'] = self.request.GET.get('search')

        # Level filters
        level_fr = self.request.GET.get('level_fr')
        level_ar = self.request.GET.get('level_ar')

        if level_fr:
            context['level_fr'] = int(level_fr)
        elif level_ar:
            context['level_ar'] = int(level_ar)

        # Check if Arabic school
        context['is_arabic_school'] = self.request.user.school.education == 'A'

        return context

    def get_queryset(self):
        return self.sort(self.request.GET, super().get_queryset(), [
            'student__last_name', 'student__first_name'
        ])
