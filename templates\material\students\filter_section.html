{% load widget_tweaks %}
{% load humanize %}

<!-- Material Design Filters Section -->
<div class="filters-section-material" hx-vals='{"statut": "{{active_nav}}", "education": "{{ active_nav }}"}'>
    <!-- Quick Actions Bar -->
    <div class="quick-actions-bar">
        <!-- Add Student Button -->
        {% if perms.school.add_enrollment and not hide_add_btn %}
        {% if school_plan != PLAN_LEVEL %}
            <button class="mdc-button mdc-button--raised show-on-pc" 
                    hx-get="{% url 'school:student_add' %}" 
                    hx-target="#dialog-xl">
                <div class="mdc-button__ripple"></div>
                <span class="material-icons mdc-button__icon">person_add</span>
                <span class="mdc-button__label">Ajouter élève</span>
            </button>
            
            <button class="mdc-button mdc-button--raised show-on-phone" 
                    hx-get="{% url 'school:student_add_wizard' %}" 
                    hx-target="#dialog">
                <div class="mdc-button__ripple"></div>
                <span class="material-icons mdc-button__icon">person_add</span>
                <span class="mdc-button__label">Ajouter</span>
            </button>
        {% else %}
            <button class="mdc-button mdc-button--raised" 
                    hx-get="{% url 'school:student_add' %}" 
                    hx-target="#dialog-xl">
                <div class="mdc-button__ripple"></div>
                <span class="material-icons mdc-button__icon">person_add</span>
                <span class="mdc-button__label">Ajouter élève</span>
            </button>
        {% endif %}
        {% endif %}

        <!-- Filter Toggle Button -->
        <button class="mdc-icon-button filter-toggle-btn" id="materialFilterToggle" aria-label="Toggle filters">
            <div class="mdc-icon-button__ripple"></div>
            <span class="material-icons">tune</span>
        </button>
    </div>

    <!-- Expandable Filter Panel -->
    <div class="filter-panel-material" id="materialFilterPanel" style="display: none;">
        <div class="mdc-card mdc-card--outlined filter-card">
            <div class="filter-card-content">
                <div class="filter-row">
                    <!-- Level Filter -->
                    <div class="filter-group">
                        <div class="mdc-select mdc-select--outlined filter-select">
                            <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                                <span class="mdc-select__selected-text">
                                    {% if request.GET.generic_level_fr %}{{ generic_level_fr_filter }}{% else %}Tous les niveaux{% endif %}
                                </span>
                                <span class="mdc-select__dropdown-icon">
                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                    </svg>
                                </span>
                                <span class="mdc-notched-outline">
                                    <span class="mdc-notched-outline__leading"></span>
                                    <span class="mdc-notched-outline__notch">
                                        <label class="mdc-floating-label">Niveau</label>
                                    </span>
                                    <span class="mdc-notched-outline__trailing"></span>
                                </span>
                            </div>
                            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                <ul class="mdc-list" role="listbox">
                                    <li class="mdc-list-item {% if not request.GET.generic_level_fr %}mdc-list-item--selected{% endif %}" 
                                        role="option" data-value=""
                                        hx-get="{{ request.path }}"
                                        hx-target="#app-content"
                                        hx-include="[name='search'], [name='per_page']">
                                        <span class="mdc-list-item__text">Tous les niveaux</span>
                                    </li>
                                    {% for level in filter_form.generic_level_fr.field.queryset %}
                                    <li class="mdc-list-item {% if request.GET.generic_level_fr == level.pk|stringformat:'s' %}mdc-list-item--selected{% endif %}" 
                                        role="option" data-value="{{ level.pk }}"
                                        hx-get="{{ request.path }}"
                                        hx-target="#app-content"
                                        hx-include="[name='search'], [name='per_page']"
                                        hx-vals='{"generic_level_fr": "{{ level.pk }}"}'>
                                        <span class="mdc-list-item__text">{{ level.name }}</span>
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Class Filter -->
                    <div class="filter-group">
                        <div class="mdc-select mdc-select--outlined filter-select">
                            <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                                <span class="mdc-select__selected-text">
                                    {% if request.GET.level_fr %}{{ level_fr_filter }}{% else %}Toutes les classes{% endif %}
                                </span>
                                <span class="mdc-select__dropdown-icon">
                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                    </svg>
                                </span>
                                <span class="mdc-notched-outline">
                                    <span class="mdc-notched-outline__leading"></span>
                                    <span class="mdc-notched-outline__notch">
                                        <label class="mdc-floating-label">Classe</label>
                                    </span>
                                    <span class="mdc-notched-outline__trailing"></span>
                                </span>
                            </div>
                            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                <ul class="mdc-list" role="listbox">
                                    <li class="mdc-list-item {% if not request.GET.level_fr %}mdc-list-item--selected{% endif %}" 
                                        role="option" data-value=""
                                        hx-get="{{ request.path }}"
                                        hx-target="#app-content"
                                        hx-include="[name='search'], [name='per_page']">
                                        <span class="mdc-list-item__text">Toutes les classes</span>
                                    </li>
                                    {% for level in filter_form.level_fr.field.queryset %}
                                    <li class="mdc-list-item {% if request.GET.level_fr == level.pk|stringformat:'s' %}mdc-list-item--selected{% endif %}" 
                                        role="option" data-value="{{ level.pk }}"
                                        hx-get="{{ request.path }}"
                                        hx-target="#app-content"
                                        hx-include="[name='search'], [name='per_page']"
                                        hx-vals='{"level_fr": "{{ level.pk }}"}'>
                                        <span class="mdc-list-item__text">{{ level.name }}</span>
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Gender Filter -->
                    <div class="filter-group">
                        <div class="mdc-select mdc-select--outlined filter-select">
                            <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                                <span class="mdc-select__selected-text">
                                    {% if request.GET.gender == 'F' %}Filles{% elif request.GET.gender == 'M' %}Garçons{% else %}Tous les genres{% endif %}
                                </span>
                                <span class="mdc-select__dropdown-icon">
                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                    </svg>
                                </span>
                                <span class="mdc-notched-outline">
                                    <span class="mdc-notched-outline__leading"></span>
                                    <span class="mdc-notched-outline__notch">
                                        <label class="mdc-floating-label">Genre</label>
                                    </span>
                                    <span class="mdc-notched-outline__trailing"></span>
                                </span>
                            </div>
                            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                <ul class="mdc-list" role="listbox">
                                    <li class="mdc-list-item {% if not request.GET.gender %}mdc-list-item--selected{% endif %}" 
                                        role="option" data-value=""
                                        hx-get="{{ request.path }}"
                                        hx-target="#app-content"
                                        hx-include="[name='search'], [name='per_page']">
                                        <span class="mdc-list-item__text">Tous les genres</span>
                                    </li>
                                    <li class="mdc-list-item {% if request.GET.gender == 'M' %}mdc-list-item--selected{% endif %}" 
                                        role="option" data-value="M"
                                        hx-get="{{ request.path }}"
                                        hx-target="#app-content"
                                        hx-include="[name='search'], [name='per_page']"
                                        hx-vals='{"gender": "M"}'>
                                        <span class="mdc-list-item__text">Garçons</span>
                                    </li>
                                    <li class="mdc-list-item {% if request.GET.gender == 'F' %}mdc-list-item--selected{% endif %}" 
                                        role="option" data-value="F"
                                        hx-get="{{ request.path }}"
                                        hx-target="#app-content"
                                        hx-include="[name='search'], [name='per_page']"
                                        hx-vals='{"gender": "F"}'>
                                        <span class="mdc-list-item__text">Filles</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Filters Chips -->
    <div class="active-filters-chips">
        {% if request.GET.generic_level_fr %}
        <div class="mdc-chip filter-chip" role="row">
            <div class="mdc-chip__ripple"></div>
            <span class="mdc-chip__icon mdc-chip__icon--leading material-icons">school</span>
            <span class="mdc-chip__text">Niveau: {{ generic_level_fr_filter }}</span>
            <button class="mdc-chip__icon mdc-chip__icon--trailing material-icons" 
                    tabindex="-1"
                    hx-get="{{ request.path }}"
                    hx-target="#app-content"
                    hx-include="[name='search'], [name='per_page']">cancel</button>
        </div>
        {% endif %}
        
        {% if request.GET.level_fr %}
        <div class="mdc-chip filter-chip" role="row">
            <div class="mdc-chip__ripple"></div>
            <span class="mdc-chip__icon mdc-chip__icon--leading material-icons">class</span>
            <span class="mdc-chip__text">Classe: {{ level_fr_filter }}</span>
            <button class="mdc-chip__icon mdc-chip__icon--trailing material-icons" 
                    tabindex="-1"
                    hx-get="{{ request.path }}"
                    hx-target="#app-content"
                    hx-include="[name='search'], [name='per_page']">cancel</button>
        </div>
        {% endif %}
        
        {% if request.GET.gender %}
        <div class="mdc-chip filter-chip" role="row">
            <div class="mdc-chip__ripple"></div>
            <span class="mdc-chip__icon mdc-chip__icon--leading material-icons">person</span>
            <span class="mdc-chip__text">Genre: {% if request.GET.gender == 'F' %}Filles{% else %}Garçons{% endif %}</span>
            <button class="mdc-chip__icon mdc-chip__icon--trailing material-icons" 
                    tabindex="-1"
                    hx-get="{{ request.path }}"
                    hx-target="#app-content"
                    hx-include="[name='search'], [name='per_page']">cancel</button>
        </div>
        {% endif %}
    </div>
</div>

<style>
.filters-section-material {
    margin-bottom: 24px;
}

.quick-actions-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.filter-panel-material {
    margin-bottom: 16px;
}

.filter-card {
    padding: 16px;
}

.filter-card-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.filter-row {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-select {
    width: 100%;
}

.active-filters-chips {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.filter-chip {
    margin: 0;
}

@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: unset;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterToggle = document.getElementById('materialFilterToggle');
    const filterPanel = document.getElementById('materialFilterPanel');
    
    if (filterToggle && filterPanel) {
        filterToggle.addEventListener('click', function() {
            if (filterPanel.style.display === 'none') {
                filterPanel.style.display = 'block';
                filterToggle.querySelector('.material-icons').textContent = 'expand_less';
            } else {
                filterPanel.style.display = 'none';
                filterToggle.querySelector('.material-icons').textContent = 'tune';
            }
        });
    }
});
</script>
