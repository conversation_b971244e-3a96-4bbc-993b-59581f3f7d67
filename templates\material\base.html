{% load static %}
{% load pwa %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block page_title %}EcolePro | Gestion des écoles{% endblock %}</title>

    <!-- Material Icons and Roboto Font -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Components Web CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">

    <!-- CSS Variables -->
    <link rel="stylesheet" href="{% static 'material/css/variables.css' %}">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'material/css/styles.css' %}">
    {% block extra_css %}{% endblock %}

    <!-- Progressive Web App Meta -->
    {% progressive_web_app_meta %}
</head>
<body>
    <!-- Material Design Preloader -->
    <div class="preloader">
        <div class="spinner">
            <svg class="circular" viewBox="25 25 50 50">
                <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="3" stroke-miterlimit="10"/>
            </svg>
        </div>
    </div>

    <div class="app-container">
        <!-- Top App Bar -->
        <header class="mdc-top-app-bar app-bar">
            <div class="mdc-top-app-bar__row">
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-start">
                    <button class="material-icons mdc-top-app-bar__navigation-icon mdc-icon-button" id="menu-button" aria-label="Menu">
                        <div class="mdc-icon-button__ripple"></div>
                        menu
                    </button>
                    <span class="mdc-top-app-bar__title">EcolePro</span>
                </section>
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-end">
                    <!-- Desktop Search Bar (visible on larger screens) -->
                    <div class="search-container desktop-search">
                        <div class="mdc-text-field mdc-text-field--filled mdc-text-field--with-leading-icon mdc-text-field--no-label search-field">
                            <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">search</span>
                            <input class="mdc-text-field__input" type="text" placeholder="Rechercher...">
                            <span class="mdc-line-ripple"></span>
                        </div>
                    </div>

                    <!-- Mobile Search Icon (visible on smaller screens) -->
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button mobile-search-icon" id="mobile-search-button" aria-label="Search">
                        <div class="mdc-icon-button__ripple"></div>
                        search
                    </button>

                    <!-- Mobile Search Bar (hidden by default, shown when search icon is clicked) -->
                    <div class="mobile-search-container" id="mobile-search-container">
                        <div class="mobile-search-inner">
                            <span class="material-icons mobile-search-icon">search</span>
                            <input type="text" class="mobile-search-input" placeholder="Rechercher...">
                            <button class="material-icons mdc-icon-button mobile-search-close" id="mobile-search-close">
                                <div class="mdc-icon-button__ripple"></div>
                                close
                            </button>
                        </div>
                    </div>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="theme-toggle-button" aria-label="Toggle dark mode">
                        <div class="mdc-icon-button__ripple"></div>
                        light_mode
                    </button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" aria-label="Notifications" style="display: none">
                        <div class="mdc-icon-button__ripple"></div>
                        notifications
                    </button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" aria-label="User">
                        <div class="mdc-icon-button__ripple"></div>
                        account_circle
                    </button>
                </section>
            </div>
        </header>

        <!-- HTMX Progress Indicator -->
        <div class="htmx-progress-bar" id="htmx-progress-bar"></div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Sidebar Backdrop -->
            <div class="sidebar-backdrop" id="sidebar-backdrop"></div>

            <!-- Sidebar -->
            <aside class="sidebar closed" id="sidebar">
                <div class="mdc-drawer__header">
                    <h3 class="mdc-drawer__title">EcolePro</h3>
                    <h6 class="mdc-drawer__subtitle">Navigation</h6>
                </div>
                <div class="mdc-drawer__content">
                    <nav class="mdc-list">
                        {% block sidebar_menu %}
                        <a class="mdc-list-item mdc-list-item--activated" href="#" aria-current="page" hx-get="{% url 'school:home' %}" hx-push-url="true" hx-target="#page-content">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">dashboard</span>
                            <span class="mdc-list-item__text">Tableau de bord</span>
                        </a>
                        <a class="mdc-list-item" href="#" hx-get="{% url 'school:material_students' %}" hx-push-url="true" hx-target="#page-content">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">people</span>
                            <span class="mdc-list-item__text">Élèves</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">school</span>
                            <span class="mdc-list-item__text">Classes</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">person</span>
                            <span class="mdc-list-item__text">Enseignants</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">receipt_long</span>
                            <span class="mdc-list-item__text">Paiements</span>
                        </a>
                        <!-- Analytics with nested subitems -->
                        <div class="mdc-list-item mdc-list-item--collapsible">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">analytics</span>
                            <span class="mdc-list-item__text">Statistiques</span>
                            <button class="mdc-icon-button mdc-list-item__meta" aria-label="Expand Analytics menu">
                                <div class="mdc-icon-button__ripple"></div>
                                <span class="material-icons">expand_more</span>
                            </button>
                        </div>
                        <div class="mdc-list-group mdc-list-group--hidden">
                            <a class="mdc-list-item mdc-list-item--nested" href="#">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic">bar_chart</span>
                                <span class="mdc-list-item__text">Statistiques des notes</span>
                            </a>
                            <a class="mdc-list-item mdc-list-item--nested" href="#">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic">trending_up</span>
                                <span class="mdc-list-item__text">Évolution</span>
                            </a>
                        </div>

                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">bar_chart</span>
                            <span class="mdc-list-item__text">Rapports</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">settings</span>
                            <span class="mdc-list-item__text">Paramètres</span>
                        </a>
                        {% endblock %}
                    </nav>
                </div>
            </aside>

            <!-- Dashboard Content -->
            <main class="dashboard-content">
                <div class="page-content" id="page-content">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>

    <!-- User Profile Offcanvas (Custom Implementation) -->
    <div class="user-drawer-container" id="user-drawer-container">
        <div class="user-drawer-backdrop" id="user-drawer-backdrop"></div>
        <div class="user-drawer" id="user-drawer">
            <div class="user-drawer-header">
                <button class="material-icons mdc-icon-button close-drawer-button" id="close-user-drawer">
                    <div class="mdc-icon-button__ripple"></div>
                    close
                </button>
                <div class="user-profile-header">
                    <div class="user-avatar">
                        <span class="material-icons">account_circle</span>
                    </div>
                    <h3 class="user-drawer-title">{{ user.get_full_name|default:user.username }}</h3>
                    <h6 class="user-drawer-subtitle">{{ user.email }}</h6>
                </div>
            </div>
            <form action="{% url 'logout' %}" class="user-drawer-content" method="post">
                {% csrf_token %}
                <nav class="mdc-list">
                    <a class="mdc-list-item" href="#" style="--item-index: 0;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">person</span>
                        <span class="mdc-list-item__text">Mon Profil</span>
                    </a>
                    <a class="mdc-list-item" href="#" style="--item-index: 1;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">settings</span>
                        <span class="mdc-list-item__text">Paramètres</span>
                    </a>
                    <a class="mdc-list-item" href="#" style="--item-index: 2;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">help</span>
                        <span class="mdc-list-item__text">Aide & Support</span>
                    </a>
                    <hr class="mdc-list-divider">
                    <button class="mdc-list-item" type="submit" style="--item-index: 3;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">logout</span>
                        <span class="mdc-list-item__text">Déconnexion</span>
                    </button>
                </nav>
            </form>
        </div>
    </div>

    <!-- Bottom App Bar for Mobile -->
    <div class="bottom-app-bar" id="bottom-app-bar">
        <div class="bottom-app-bar-inner">
            <button class="bottom-nav-item active" hx-get="{% url 'school:home' %}" hx-target="#page-content" hx-push-url="{% url 'school:home' %}">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">dashboard</span>
                <span class="bottom-nav-label">Accueil</span>
            </button>
            <button class="bottom-nav-item" hx-get="{% url 'school:material_students' %}" hx-target="#page-content" hx-push-url="{% url 'school:material_students' %}">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">people</span>
                <span class="bottom-nav-label">Élèves</span>
            </button>
            <button class="bottom-nav-item">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">school</span>
                <span class="bottom-nav-label">Classes</span>
            </button>
            <button class="bottom-nav-item">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">receipt_long</span>
                <span class="bottom-nav-label">Paiements</span>
            </button>
            <button class="bottom-nav-item">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">more_horiz</span>
                <span class="bottom-nav-label">Plus</span>
            </button>
        </div>
    </div>

    <!-- Notifications Menu -->
    <div class="mdc-menu-surface--anchor notifications-anchor">
        <div class="mdc-menu mdc-menu-surface notifications-menu" id="notifications-menu">
            <div class="notifications-header">
                <h3>Notifications</h3>
                <button class="mdc-button" id="mark-all-read">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Tout marquer comme lu</span>
                </button>
            </div>
            <div class="notifications-content">
                <div class="notification-item unread">
                    <div class="notification-icon">
                        <span class="material-icons">payment</span>
                    </div>
                    <div class="notification-details">
                        <div class="notification-title">Nouveau paiement reçu</div>
                        <div class="notification-message">Un paiement de 50 000 FCFA a été reçu pour l'élève John Doe.</div>
                        <div class="notification-time">Il y a 5 minutes</div>
                    </div>
                </div>
                <div class="notification-item">
                    <div class="notification-icon">
                        <span class="material-icons">person_add</span>
                    </div>
                    <div class="notification-details">
                        <div class="notification-title">Nouvel élève inscrit</div>
                        <div class="notification-message">Jane Smith a été inscrite en classe de 6ème.</div>
                        <div class="notification-time">Il y a 2 heures</div>
                    </div>
                </div>
                <div class="notification-item">
                    <div class="notification-icon">
                        <span class="material-icons">event</span>
                    </div>
                    <div class="notification-details">
                        <div class="notification-title">Rappel: Réunion des enseignants</div>
                        <div class="notification-message">La réunion des enseignants aura lieu demain à 10h.</div>
                        <div class="notification-time">Il y a 1 jour</div>
                    </div>
                </div>
            </div>
            <div class="notifications-footer">
                <a href="#" class="mdc-button">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Voir toutes les notifications</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Modal Container -->
    <div class="modal-container" id="modal-container">
        <div class="modal-backdrop" id="modal-backdrop"></div>
        <div class="modal-content" id="modal-content">
            <!-- Modal content will be loaded here -->
        </div>
    </div>

    <!-- Material Components Web JavaScript -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="{% static 'material/js/script.js' %}"></script>
    <script src="{% static 'material/js/data-table-actions.js' %}"></script>
    {% block extra_js %}{% endblock %}

    <!-- HTMX for dynamic content loading -->
    <script src="{% static 'js/htmx.min.js' %}"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js" defer></script>

    {% block javascript %}{% endblock %}
</body>
</html>
