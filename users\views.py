from django.contrib.auth.views import LoginView
from django.contrib.auth import login as auth_login, logout as auth_logout
from django.db.transaction import atomic
from django.contrib.auth.hashers import make_password
from django.contrib.auth.models import Group
from django.contrib.auth.forms import PasswordChangeForm
from django.contrib.auth import mixins, update_session_auth_hash
from django.http import HttpResponse, HttpResponseRedirect
from django.views import generic
from django.urls import reverse
from django.shortcuts import render
from django.conf import settings
from typing import Any
from .models import CustomUser
from school.models import Year, Teacher, Subscription
from school.views import BaseHTMXRequestsView, get_session_year
from school.school_utils import get_current_year
from users.forms import UserForm
from main import utils

def get_choice_key(description, choice_list):
    for choice_item in choice_list:
        if str(choice_item[1]).lower() == str(description).lower():
            return choice_item[0]

class CustomLoginView(LoginView):
    template_name = 'material/auth/login.html'

    def form_valid(self, form):
        auth_login(self.request, form.get_user())
        user = self.request.user
        year = self.request.POST['year']
        queryset = Year.objects.filter(pk=year)
        if not queryset.exists():
            year = get_current_year().name
        else:
            year = queryset.first().name

        if not user.has_active_subscription(user.school_id, year):
            auth_logout(self.request)
            form.add_error(
                'password',
                "Vous n'avez pas souscrit à un abonnement pour l'année scolaire choisie. " +
                " Veuillez contacter le +225 07 59 95 14 53 / 05 45 84 55 98 pour plus de détails")
            return super().form_invalid(form)

        self.request.session[f'{user.id}'] = year
        self.request.session['PLAN_LEVEL'] = Subscription.PLAN_LEVEL
        self.request.session[f'school_plan_{user.id}'] = user.get_school_subscription(
            user.school.id if user.school else 0, year
        ).plan
        return HttpResponseRedirect(reverse('school:home'))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['years'] = Year.objects.only('id', 'name', 'full_name')
        context['active_year'] = get_current_year()
        context['TEST_ACCOUNT_DAYS'] = settings.TEST_ACCOUNT_DAYS
        return context


class UsersListView(mixins.PermissionRequiredMixin,
                    BaseHTMXRequestsView, generic.ListView):
    model = CustomUser
    context_object_name = 'users'
    template_name = 'partials/user/users_list.html'
    permission_required = 'users.view_customuser'

    def get_queryset(self):
        user = self.request.user
        year = get_session_year(self.request)
        queryset = CustomUser.for_user.get_all(user, year)
        role = self.request.GET.get('role')

        if role:
            role = role.lower()
            role = get_choice_key(role, CustomUser.ROLE_CHOICES)
            queryset = queryset.filter(role=role)
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['USER_ROLES'] = CustomUser.ROLE_CHOICES
        context['ROLE_FOUNDER'] = utils.ROLE_FOUNDER
        context['selected_role'] = self.request.GET.get('role')
        context['DEFAULT_PASSWORD'] = utils.DEFAULT_PASSWORD
        context['page_title'] = 'Gestion des utilisateurs et des comptes'
        return context


class UserCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    model = CustomUser
    form_class = UserForm
    template_name = 'partials/user/user_form.html'
    permission_required = 'users.add_customuser'

    def get_teacher(self):
        teacher_id = self.request.GET.get('teacher_id')
        if teacher_id:
            return Teacher.objects.for_school(
                school=self.request.user.school
            ).filter(id=teacher_id).first()
        return None

    def get_context_data(self, **kwargs: Any):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        user_role = user.role

        teacher_id = self.request.GET.get('teacher_id')
        teacher = None
        form = context['form']

        if teacher_id:
            teacher = self.get_teacher()
            form.fields['last_name'].initial = teacher.last_name
            form.fields['first_name'].initial = teacher.first_name
            form.fields['phone'].initial = teacher.phone
            form.fields['teacher_id'].initial = teacher_id

        # Make phone field required
        form.fields['phone'].widget.attrs['required'] = 'required'

        allowed_choices = ((utils.ROLE_TEACHER, 'Enseignant',),)
        if user_role == utils.ROLE_FOUNDER and not teacher_id:
            allowed_choices = (
                (utils.ROLE_ACCOUNTANT, 'Comptable'),
                (utils.ROLE_COMPUTER_SCIENTIST, 'Informaticien'),
                # (utils.ROLE_DIRECTOR, 'Directeur'),
            )

        if user.school.education == utils.EDUCATION_FRENCH and \
            'education' in form.fields:
            del form.fields['education']

        form.fields['role'].choices = allowed_choices
        form.fields['last_name'].widget.attrs['required'] = 'required'
        form.fields['first_name'].widget.attrs['required'] = 'required'
        form.fields['custom_password'].widget.attrs['required'] = False
        context['DEFAULT_PASSWORD'] = utils.DEFAULT_PASSWORD
        context['teacher_id'] = teacher_id
        return context

    def handle_no_permission(self):
        return HttpResponse(status=403, headers={'HX-Trigger': 'permission_denied'})

    @atomic
    def form_valid(self, form):
        cd = form.cleaned_data
        user_password =cd['custom_password']

        with atomic():
            user = form.save(False)
            user, group_name = utils.create_user(
                user, user_password,
                self.request.user.school, get_current_year(),
                username=cd['phone'],
            )

            teacher = None
            teacher_id = form.cleaned_data.get('teacher_id')

            group = None
            if teacher_id:
                teacher = Teacher.objects.for_school(
                    self.request.user.school
                ).filter(id=teacher_id).first()


            if teacher and not teacher.user:
                teacher.user = user
                teacher.save(update_fields=['user'])
            user.save(update_fields=['username'])

            group, created = Group.objects.get_or_create(name=group_name)
            group.user_set.add(user)
            return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})


class UserUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = CustomUser
    fields = ['last_name', 'first_name', 'phone']
    template_name = 'partials/user/user_form.html'
    permission_required = 'users.change_customuser'

    def form_valid(self, form):
        form.save(True)
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})


class ToggleActiveStatusView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = CustomUser
    fields = []
    template_name = 'partials/confirm.html'
    permission_required = 'users.change_customuser'

    def form_valid(self, form):
        user = self.get_object()

        if user.is_active:
            user.is_active = False
        else:
            user.is_active = True
        user.save(update_fields=['is_active'])

        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})


class ResetPasswordView(mixins.PermissionRequiredMixin, generic.UpdateView):
    model = CustomUser
    fields = []
    template_name = 'partials/confirm.html'
    permission_required = 'users.change_customuser'

    def form_valid(self, form):
        user = self.get_object()
        user.password = make_password(utils.DEFAULT_PASSWORD)
        user.custom_password = utils.DEFAULT_PASSWORD
        user.save(update_fields=['password', 'custom_password'])
        return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})


def change_password(request):
    if request.method == 'POST':
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            print(form.cleaned_data)
            user.custom_password = form.cleaned_data['new_password1']
            user.save()
            update_session_auth_hash(request, user)  # Important!
            return HttpResponse(status=204, headers={'HX-Trigger': 'saved'})

    else:
        form = PasswordChangeForm(request.user)
    return render(request, 'partials/user/password_change.html',
        {'form': form})


def show_user_password(request, user_id):
    user = CustomUser.objects.get(id=user_id)
    return HttpResponse(f"<span class='text-muted'>{user.custom_password}</span>")